/**
 * 数组映射预览和调整组件
 * 提供拖拽交互界面，支持用户手动调整项目间的映射关系
 */

// Alpine.js 映射预览组件
document.addEventListener('alpine:init', () => {
    Alpine.data('mappingPreview', () => ({
        // 状态管理
        isVisible: false,
        currentModule: null,
        mappingResult: null,
        userMappings: [],
        
        // 拖拽状态
        draggedItem: null,
        draggedType: null, // 'source' 或 'target'
        dropZones: [],
        
        // 显示选项
        showExactMatches: true,
        showFuzzyMatches: true,
        showUnmatched: true,
        
        // 初始化
        init() {
            this.setupDragAndDrop();
        },
        
        // 显示映射预览
        show(moduleName, mappingResult) {
            this.currentModule = moduleName;
            this.mappingResult = mappingResult;
            this.userMappings = [];
            this.isVisible = true;
            
            // 初始化用户映射（基于现有的精确匹配）
            this.initializeUserMappings();
            
            console.log('显示映射预览:', moduleName, mappingResult);
        },
        
        // 隐藏映射预览
        hide() {
            this.isVisible = false;
            this.currentModule = null;
            this.mappingResult = null;
            this.userMappings = [];
            this.clearDragState();
        },
        
        // 初始化用户映射
        initializeUserMappings() {
            // 添加精确匹配
            this.mappingResult.exactMatches.forEach(match => {
                this.userMappings.push({
                    id: `exact_${match.sourceIndex}_${match.targetIndex}`,
                    sourceIndex: match.sourceIndex,
                    targetIndex: match.targetIndex,
                    action: 'map',
                    confidence: match.confidence,
                    type: 'exact'
                });
            });
            
            // 添加模糊匹配（待确认）
            this.mappingResult.fuzzyMatches.forEach(match => {
                this.userMappings.push({
                    id: `fuzzy_${match.sourceIndex}_${match.targetIndex}`,
                    sourceIndex: match.sourceIndex,
                    targetIndex: match.targetIndex,
                    action: 'map',
                    confidence: match.confidence,
                    type: 'fuzzy',
                    needsConfirmation: true
                });
            });
        },
        
        // 设置拖拽功能
        setupDragAndDrop() {
            // 这里可以添加更复杂的拖拽逻辑
            console.log('拖拽功能已设置');
        },
        
        // 开始拖拽
        startDrag(item, type, event) {
            this.draggedItem = item;
            this.draggedType = type;
            
            // 设置拖拽效果
            if (event.dataTransfer) {
                event.dataTransfer.effectAllowed = 'move';
                event.dataTransfer.setData('text/plain', JSON.stringify({
                    item: item,
                    type: type
                }));
            }
            
            console.log('开始拖拽:', type, item);
        },
        
        // 拖拽结束
        endDrag() {
            this.clearDragState();
        },
        
        // 清除拖拽状态
        clearDragState() {
            this.draggedItem = null;
            this.draggedType = null;
            this.dropZones = [];
        },
        
        // 处理放置
        handleDrop(targetItem, targetType, event) {
            event.preventDefault();
            
            if (!this.draggedItem || !targetItem) return;
            
            // 创建新的映射
            const newMapping = {
                id: `user_${Date.now()}`,
                sourceIndex: this.draggedType === 'source' ? this.draggedItem.index : targetItem.index,
                targetIndex: this.draggedType === 'target' ? this.draggedItem.index : targetItem.index,
                action: 'map',
                confidence: 0.5, // 用户手动映射
                type: 'manual'
            };
            
            // 移除冲突的映射
            this.removeConflictingMappings(newMapping);
            
            // 添加新映射
            this.userMappings.push(newMapping);
            
            this.clearDragState();
            console.log('创建新映射:', newMapping);
        },
        
        // 移除冲突的映射
        removeConflictingMappings(newMapping) {
            this.userMappings = this.userMappings.filter(mapping => 
                mapping.sourceIndex !== newMapping.sourceIndex && 
                mapping.targetIndex !== newMapping.targetIndex
            );
        },
        
        // 删除映射
        removeMapping(mappingId) {
            this.userMappings = this.userMappings.filter(m => m.id !== mappingId);
        },
        
        // 标记项目为删除
        markAsDeleted(sourceIndex) {
            // 移除现有映射
            this.userMappings = this.userMappings.filter(m => m.sourceIndex !== sourceIndex);
            
            // 添加删除标记
            this.userMappings.push({
                id: `delete_${sourceIndex}`,
                sourceIndex: sourceIndex,
                targetIndex: null,
                action: 'delete',
                type: 'manual'
            });
        },
        
        // 标记项目为新增
        markAsNew(targetIndex) {
            // 移除现有映射
            this.userMappings = this.userMappings.filter(m => m.targetIndex !== targetIndex);
            
            // 添加新增标记
            this.userMappings.push({
                id: `new_${targetIndex}`,
                sourceIndex: null,
                targetIndex: targetIndex,
                action: 'new',
                type: 'manual'
            });
        },
        
        // 确认模糊匹配
        confirmFuzzyMatch(mappingId, confirmed) {
            const mapping = this.userMappings.find(m => m.id === mappingId);
            if (mapping && mapping.type === 'fuzzy') {
                if (confirmed) {
                    mapping.needsConfirmation = false;
                    mapping.confirmed = true;
                } else {
                    // 移除映射
                    this.removeMapping(mappingId);
                }
            }
        },
        
        // 获取源项目的映射状态
        getSourceMappingStatus(sourceIndex) {
            const mapping = this.userMappings.find(m => m.sourceIndex === sourceIndex);
            if (!mapping) return 'unmapped';
            
            switch (mapping.action) {
                case 'map': return mapping.type === 'fuzzy' && mapping.needsConfirmation ? 'fuzzy' : 'mapped';
                case 'delete': return 'deleted';
                case 'merge': return 'merged';
                default: return 'unmapped';
            }
        },
        
        // 获取目标项目的映射状态
        getTargetMappingStatus(targetIndex) {
            const mapping = this.userMappings.find(m => m.targetIndex === targetIndex);
            if (!mapping) return 'unmapped';
            
            switch (mapping.action) {
                case 'map': return mapping.type === 'fuzzy' && mapping.needsConfirmation ? 'fuzzy' : 'mapped';
                case 'new': return 'new';
                default: return 'unmapped';
            }
        },
        
        // 获取映射统计
        getMappingStats() {
            const stats = {
                total: this.userMappings.length,
                confirmed: this.userMappings.filter(m => !m.needsConfirmation).length,
                pending: this.userMappings.filter(m => m.needsConfirmation).length,
                byAction: {
                    map: this.userMappings.filter(m => m.action === 'map').length,
                    delete: this.userMappings.filter(m => m.action === 'delete').length,
                    new: this.userMappings.filter(m => m.action === 'new').length,
                    merge: this.userMappings.filter(m => m.action === 'merge').length
                }
            };
            return stats;
        },
        
        // 应用映射配置
        applyMappings() {
            if (!this.currentModule) return;
            
            // 调用父组件的方法应用映射
            this.$dispatch('apply-mappings', {
                moduleName: this.currentModule,
                userMappings: this.userMappings
            });
            
            this.hide();
        },
        
        // 重置映射
        resetMappings() {
            this.userMappings = [];
            this.initializeUserMappings();
        },
        
        // 获取项目显示文本
        getItemDisplayText(item, keyFields) {
            if (!keyFields || keyFields.length === 0) {
                return `项目 ${item.index + 1}`;
            }
            
            const keyValues = keyFields.map(field => {
                const value = this.getNestedValue(item.data, field);
                return value ? String(value) : '';
            }).filter(v => v);
            
            return keyValues.length > 0 ? keyValues.join(' | ') : `项目 ${item.index + 1}`;
        },
        
        // 获取嵌套值
        getNestedValue(obj, path) {
            return path.split('.').reduce((current, key) => {
                return current && current[key] !== undefined ? current[key] : undefined;
            }, obj);
        },
        
        // 格式化置信度
        formatConfidence(confidence) {
            return Math.round(confidence * 100) + '%';
        },
        
        // 获取置信度样式类
        getConfidenceClass(confidence) {
            if (confidence >= 0.9) return 'text-success';
            if (confidence >= 0.7) return 'text-warning';
            return 'text-danger';
        }
    }));
});
