<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpine.js 错误修复测试</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="../css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Alpine.js 错误修复测试</h2>
        
        <div x-data="testApp" class="card">
            <div class="card-header">
                <h5>测试结果</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 x-text="getFirstLayerChangeCount()"></h5>
                                <small>第一层变更</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 x-text="getSecondLayerChangeCount()"></h5>
                                <small>第二层变更</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 x-text="getAllChangesCount()"></h5>
                                <small>总变更数</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="btn-group mb-3" role="group">
                    <button type="button" class="btn btn-sm" 
                            :class="currentLayer === 1 ? 'btn-primary' : 'btn-outline-primary'"
                            @click="switchLayer(1)">
                        第一层
                        <span class="badge bg-light text-dark ms-1" x-text="getFirstLayerChangeCount()"></span>
                    </button>
                    <button type="button" class="btn btn-sm" 
                            :class="currentLayer === 2 ? 'btn-primary' : 'btn-outline-primary'"
                            @click="switchLayer(2)">
                        第二层
                        <span class="badge bg-light text-dark ms-1" x-text="getSecondLayerChangeCount()"></span>
                    </button>
                    <button type="button" class="btn btn-sm" 
                            :class="currentLayer === 0 ? 'btn-primary' : 'btn-outline-primary'"
                            @click="switchLayer(0)">
                        全部
                        <span class="badge bg-light text-dark ms-1" x-text="getAllChangesCount()"></span>
                    </button>
                </div>

                <div class="alert alert-info">
                    <p><strong>当前层级:</strong> <span x-text="currentLayer"></span></p>
                    <p><strong>layeredResult状态:</strong> <span x-text="layeredResult ? '已加载' : '未加载'"></span></p>
                    <p><strong>当前层级变更数:</strong> <span x-text="getCurrentLayerChanges().length"></span></p>
                </div>

                <button @click="simulateDataLoad()" class="btn btn-success me-2">模拟数据加载</button>
                <button @click="clearData()" class="btn btn-warning">清除数据</button>
            </div>
        </div>
    </div>

    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="./lib/<EMAIL>" defer></script>

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('testApp', () => ({
                // 分层对比结果
                layeredResult: null,
                currentLayer: 1,

                // 切换查看层级
                switchLayer(layer) {
                    this.currentLayer = layer;
                },

                // 获取当前层级的变更列表
                getCurrentLayerChanges() {
                    if (!this.layeredResult) return [];
                    
                    if (this.currentLayer === 1) {
                        return this.layeredResult.firstLayer && this.layeredResult.firstLayer.changes 
                            ? this.layeredResult.firstLayer.changes : [];
                    } else if (this.currentLayer === 2) {
                        return this.layeredResult.secondLayer && this.layeredResult.secondLayer.changes 
                            ? this.layeredResult.secondLayer.changes : [];
                    }
                    
                    return this.layeredResult.allChanges || [];
                },

                // 安全获取第一层变更数量
                getFirstLayerChangeCount() {
                    return this.layeredResult && this.layeredResult.firstLayer && this.layeredResult.firstLayer.changes 
                        ? this.layeredResult.firstLayer.changes.length : 0;
                },

                // 安全获取第二层变更数量
                getSecondLayerChangeCount() {
                    return this.layeredResult && this.layeredResult.secondLayer && this.layeredResult.secondLayer.changes 
                        ? this.layeredResult.secondLayer.changes.length : 0;
                },

                // 安全获取总变更数量
                getAllChangesCount() {
                    return this.layeredResult && this.layeredResult.allChanges 
                        ? this.layeredResult.allChanges.length : 0;
                },

                // 模拟数据加载
                simulateDataLoad() {
                    this.layeredResult = {
                        firstLayer: {
                            changes: [
                                { id: 1, type: 'ADD', path: 'test.field1' },
                                { id: 2, type: 'MODIFY', path: 'test.field2' }
                            ],
                            mergedData: { test: 'data' }
                        },
                        secondLayer: {
                            changes: [
                                { id: 3, type: 'DELETE', path: 'test.field3' }
                            ]
                        },
                        allChanges: [
                            { id: 1, type: 'ADD', path: 'test.field1' },
                            { id: 2, type: 'MODIFY', path: 'test.field2' },
                            { id: 3, type: 'DELETE', path: 'test.field3' }
                        ]
                    };
                },

                // 清除数据
                clearData() {
                    this.layeredResult = null;
                    this.currentLayer = 1;
                }
            }));
        });
    </script>
</body>
</html>
