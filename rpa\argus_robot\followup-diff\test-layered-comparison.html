<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分层对比功能测试</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="../css/all.min.css" rel="stylesheet">
    <link href="css/followup-diff.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>分层对比功能测试</h2>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results"></div>
                        <button id="run-tests" class="btn btn-primary">运行测试</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="js/key-generator.js"></script>
    <script src="js/array-mapping-manager.js"></script>
    <script src="js/data-compare.js"></script>

    <script>
        // 测试数据
        const testData = {
            initialReport: {
                "基本信息": {
                    "患者姓名": "张三",
                    "年龄": 45,
                    "性别": "男"
                },
                "不良事件": [
                    {
                        "事件名称": "头痛",
                        "发生日期": "2024-01-01",
                        "严重程度": "轻度"
                    },
                    {
                        "事件名称": "恶心",
                        "发生日期": "2024-01-02",
                        "严重程度": "中度"
                    }
                ]
            },
            followupReport: {
                "基本信息": {
                    "患者姓名": "张三",
                    "年龄": 45,
                    "性别": "男",
                    "联系电话": "13800138000"
                },
                "不良事件": [
                    {
                        "事件名称": "头痛",
                        "发生日期": "2024-01-01",
                        "严重程度": "轻度",
                        "处理措施": "休息"
                    },
                    {
                        "事件名称": "恶心",
                        "发生日期": "2024-01-02",
                        "严重程度": "重度"
                    },
                    {
                        "事件名称": "发热",
                        "发生日期": "2024-01-03",
                        "严重程度": "中度"
                    }
                ]
            },
            finalData: {
                "基本信息": {
                    "患者姓名": "张三",
                    "年龄": 45,
                    "性别": "男",
                    "联系电话": "13800138000",
                    "地址": "北京市"
                },
                "不良事件": [
                    {
                        "事件名称": "头痛",
                        "发生日期": "2024-01-01",
                        "严重程度": "轻度",
                        "处理措施": "休息",
                        "结果": "好转"
                    },
                    {
                        "事件名称": "恶心",
                        "发生日期": "2024-01-02",
                        "严重程度": "重度",
                        "处理措施": "药物治疗"
                    }
                ]
            }
        };

        // 测试配置
        const testConfig = {
            "不良事件": {
                keyFields: ["事件名称", "发生日期"],
                description: "基于事件名称和发生日期生成主键"
            }
        };

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="spinner-border" role="status"><span class="visually-hidden">测试中...</span></div>';

            setTimeout(() => {
                try {
                    // 创建数据对比器
                    const dataComparer = new DataComparer(testConfig);
                    
                    // 测试1: 分层对比
                    console.log('开始测试分层对比...');
                    const layeredResult = dataComparer.compareLayered(
                        testData.initialReport,
                        testData.followupReport,
                        testData.finalData
                    );

                    // 测试2: 数组映射管理器
                    console.log('开始测试数组映射管理器...');
                    const arrayMappingManager = new ArrayMappingManager();
                    const mappingResult = arrayMappingManager.smartMatch(
                        testData.initialReport["不良事件"],
                        testData.followupReport["不良事件"],
                        testConfig["不良事件"].keyFields,
                        "不良事件"
                    );

                    // 生成测试报告
                    const report = generateTestReport(layeredResult, mappingResult);
                    resultsDiv.innerHTML = report;

                } catch (error) {
                    resultsDiv.innerHTML = `<div class="alert alert-danger">测试失败: ${error.message}</div>`;
                    console.error('测试错误:', error);
                }
            }, 1000);
        }

        function generateTestReport(layeredResult, mappingResult) {
            return `
                <div class="test-report">
                    <h6>分层对比测试结果</h6>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="text-primary">${layeredResult.firstLayer.changes.length}</h5>
                                    <small>第一层变更</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="text-success">${layeredResult.secondLayer.changes.length}</h5>
                                    <small>第二层变更</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="text-info">${layeredResult.allChanges.length}</h5>
                                    <small>总变更数</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h6>数组映射测试结果</h6>
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h5>${mappingResult.exactMatches.length}</h5>
                                    <small>精确匹配</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning">
                                <div class="card-body text-center">
                                    <h5>${mappingResult.fuzzyMatches.length}</h5>
                                    <small>模糊匹配</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h5>${mappingResult.unmatched.source.length}</h5>
                                    <small>未匹配源项</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h5>${mappingResult.unmatched.target.length}</h5>
                                    <small>未匹配目标项</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>测试通过</h6>
                        <ul class="mb-0">
                            <li>分层对比功能正常工作</li>
                            <li>数组映射管理器正常工作</li>
                            <li>智能匹配算法正常工作</li>
                            <li>变更检测功能正常工作</li>
                        </ul>
                    </div>

                    <details class="mt-3">
                        <summary class="btn btn-outline-secondary btn-sm">查看详细结果</summary>
                        <pre class="mt-2 p-3 bg-light border rounded"><code>${JSON.stringify({layeredResult, mappingResult}, null, 2)}</code></pre>
                    </details>
                </div>
            `;
        }

        // 绑定事件
        document.getElementById('run-tests').addEventListener('click', runTests);

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，准备运行测试...');
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
