<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>病例随访数据对比工具</title>
    
    <!-- Bootstrap CSS -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/followup-diff.css" rel="stylesheet">
</head>
<body>
    <div id="app" x-data="followupDiffApp" x-init="init()">
        <!-- 顶部导航 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <span class="navbar-brand">
                    <i class="fas fa-chart-line me-2"></i>
                    病例随访数据对比工具
                </span>
                <div class="navbar-nav ms-auto">
                    <div class="btn-group me-2" x-show="dataLoaded">
                        <button class="btn btn-outline-light btn-sm" @click="startPreviewMode()" :disabled="showPreview">
                            <i class="fas fa-eye me-1"></i>
                            预览映射
                            <span x-show="getPendingMappingCount() > 0" class="badge bg-warning text-dark ms-1" x-text="getPendingMappingCount()"></span>
                        </button>
                        <button class="btn btn-outline-light btn-sm" @click="exitPreviewMode()" x-show="showPreview">
                            <i class="fas fa-times me-1"></i>
                            退出预览
                        </button>
                    </div>
                    <button class="btn btn-outline-light btn-sm" @click="loadSampleData()" :disabled="loading">
                        <i class="fas fa-download me-1"></i>
                        <span x-text="loading ? '加载中...' : '加载示例数据'"></span>
                    </button>
                </div>
            </div>
        </nav>

        <!-- 主内容区 -->
        <div class="container-fluid mt-3">
            <!-- 加载状态 -->
            <div x-show="loading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载数据...</p>
            </div>

            <!-- 错误提示 -->
            <div x-show="error" class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <span x-text="error"></span>
                <button type="button" class="btn-close" @click="error = null"></button>
            </div>

            <!-- 预览模式提示 -->
            <div x-show="showPreview" class="preview-mode-banner">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-eye me-2"></i>
                        <strong>预览模式已启用</strong> - 正在分析数组对象映射关系，请配置映射后应用变更
                        <span x-show="getPendingMappingCount() > 0" class="ms-2">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            发现 <strong x-text="getPendingMappingCount()"></strong> 个模块需要配置映射
                        </span>
                    </div>
                    <div x-show="getPendingMappingCount() > 0">
                        <a href="mapping-guide.html" target="_blank" class="btn btn-info btn-sm me-2">
                            <i class="fas fa-question-circle me-1"></i>
                            配置指南
                        </a>
                        <button class="btn btn-warning btn-sm me-2" @click="quickConfigureAllMappings()">
                            <i class="fas fa-magic me-1"></i>
                            快速配置全部
                        </button>
                        <button class="btn btn-outline-light btn-sm" @click="exitPreviewMode()">
                            <i class="fas fa-times me-1"></i>
                            退出预览
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数据对比界面 -->
            <div x-show="!loading && !error && dataLoaded" class="row">
                <!-- 左侧：三阶段数据展示 -->
                <div class="col-lg-10">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <ul class="nav nav-tabs card-header-tabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#stage-comparison" type="button" role="tab">
                                            <i class="fas fa-exchange-alt me-1"></i>
                                            阶段对比
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#change-details" type="button" role="tab">
                                            <i class="fas fa-list-ul me-1"></i>
                                            变更详情
                                        </button>
                                    </li>
                                </ul>

                                <!-- 层级切换按钮 -->
                                <div class="btn-group" x-show="layeredResult" role="group">
                                    <button type="button" class="btn btn-sm"
                                            :class="currentLayer === 1 ? 'btn-primary' : 'btn-outline-primary'"
                                            @click="switchLayer(1)">
                                        <i class="fas fa-layer-group me-1"></i>
                                        第一层
                                        <span class="badge bg-light text-dark ms-1" x-show="layeredResult" x-text="layeredResult.firstLayer?.changes?.length || 0"></span>
                                    </button>
                                    <button type="button" class="btn btn-sm"
                                            :class="currentLayer === 2 ? 'btn-primary' : 'btn-outline-primary'"
                                            @click="switchLayer(2)">
                                        <i class="fas fa-layer-group me-1"></i>
                                        第二层
                                        <span class="badge bg-light text-dark ms-1" x-show="layeredResult" x-text="layeredResult.secondLayer?.changes?.length || 0"></span>
                                    </button>
                                    <button type="button" class="btn btn-sm"
                                            :class="currentLayer === 0 ? 'btn-primary' : 'btn-outline-primary'"
                                            @click="switchLayer(0)">
                                        <i class="fas fa-list me-1"></i>
                                        全部
                                        <span class="badge bg-light text-dark ms-1" x-show="layeredResult" x-text="layeredResult.allChanges?.length || 0"></span>
                                    </button>
                                </div>
                            </div>

                            <!-- 当前层级信息 -->
                            <div x-show="layeredResult && currentLayer > 0" class="small text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                <span x-text="getLayerDescription(currentLayer)"></span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="tab-content">
                                <!-- 阶段对比视图 -->
                                <div class="tab-pane fade show active" id="stage-comparison" role="tabpanel">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <h6 class="text-primary">
                                                <i class="fas fa-file-medical me-1"></i>
                                                初次报告
                                            </h6>
                                            <div class="data-stage-container" x-html="renderDataStage(initialReport, 'initial')"></div>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-warning">
                                                <i class="fas fa-file-medical-alt me-1"></i>
                                                随访报告
                                            </h6>
                                            <div class="data-stage-container" x-html="renderDataStage(followupReport, 'followup')"></div>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-success">
                                                <i class="fas fa-database me-1"></i>
                                                已录入数据
                                            </h6>
                                            <div class="data-stage-container" x-html="renderDataStage(finalData, 'final')"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 变更详情视图 -->
                                <div class="tab-pane fade" id="change-details" role="tabpanel">
                                    <div class="change-list-container">
                                        <template x-for="(moduleChanges, moduleName) in getChangesByModule()" :key="moduleName">
                                            <div class="module-changes-section mb-4">
                                                <!-- 模块标题 -->
                                                <div class="module-header card">
                                                    <div class="card-header bg-light">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <h6 class="mb-0">
                                                                <i class="fas fa-folder me-2"></i>
                                                                <span x-text="moduleName"></span>
                                                            </h6>
                                                            <div class="d-flex align-items-center">
                                                                <div class="module-stats me-3">
                                                                    <span class="badge bg-primary me-1" x-text="getModuleChangeStats(moduleChanges).total + ' 项'"></span>
                                                                    <span class="badge bg-success me-1" x-text="getModuleChangeStats(moduleChanges).byType.ADD + ' 新增'"></span>
                                                                    <span class="badge bg-warning text-dark me-1" x-text="getModuleChangeStats(moduleChanges).byType.MODIFY + ' 修改'"></span>
                                                                    <span class="badge bg-danger" x-text="getModuleChangeStats(moduleChanges).byType.DELETE + ' 删除'"></span>
                                                                    <span x-show="getFuzzyMatchCount() > 0" class="badge bg-info me-1" x-text="getFuzzyMatchCount() + ' 待确认'"></span>
                                                                </div>
                                                                <div class="module-actions">
                                                                    <!-- 映射预览按钮 -->
                                                                    <button x-show="showPreview && pendingMappings.some(p => p.moduleName === moduleName)"
                                                                            class="btn btn-sm btn-outline-info me-1"
                                                                            @click="showArrayMappingPreview(moduleName)"
                                                                            title="配置数组映射">
                                                                        <i class="fas fa-project-diagram"></i>
                                                                    </button>
                                                                    <button class="btn btn-sm btn-outline-success me-1"
                                                                            @click="confirmAllModuleChanges(moduleChanges)"
                                                                            title="确认所有变更">
                                                                        <i class="fas fa-check-double"></i>
                                                                    </button>
                                                                    <button class="btn btn-sm btn-outline-secondary me-1"
                                                                            @click="cancelAllModuleChanges(moduleChanges)"
                                                                            title="取消所有确认">
                                                                        <i class="fas fa-times"></i>
                                                                    </button>
                                                                    <button class="btn btn-sm btn-outline-primary"
                                                                            @click="toggleAllModuleChanges(moduleChanges)"
                                                                            title="切换所有状态">
                                                                        <i class="fas fa-exchange-alt"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="mt-2">
                                                            <div class="progress" style="height: 4px;">
                                                                <div class="progress-bar bg-success"
                                                                     :style="'width: ' + (getModuleChangeStats(moduleChanges).confirmed / getModuleChangeStats(moduleChanges).total * 100) + '%'"
                                                                     x-text="getModuleChangeStats(moduleChanges).confirmed + '/' + getModuleChangeStats(moduleChanges).total + ' 已确认'"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 模块变更列表 -->
                                                <div class="module-changes-list">
                                                    <template x-for="change in moduleChanges" :key="change.id">
                                                        <div class="change-item card mb-2" :class="getChangeItemClass(change)">
                                                            <div class="card-body py-2">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <div class="flex-grow-1">
                                                                        <div class="d-flex align-items-center mb-1">
                                                                            <span class="badge me-2" :class="getChangeTypeBadgeClass(change.type)" x-text="getChangeTypeText(change.type)"></span>
                                                                            <strong class="field-path" x-text="formatDisplayPath(change.path, moduleName)"></strong>
                                                                            <span class="badge bg-light text-dark ms-2 small" x-text="change.stage === 'initial_to_followup' ? '初次→随访' : '随访→录入'"></span>
                                                                        </div>
                                                                        <div class="small text-muted">
                                                                            <div x-show="shouldShowOldValue(change)" class="mb-1">
                                                                                <span class="text-danger">原值:</span>
                                                                                <span class="change-value bg-light px-2 py-1 rounded" x-html="formatChangeValue(change.oldValue)"></span>
                                                                            </div>
                                                                            <div x-show="shouldShowNewValue(change)">
                                                                                <span class="text-success">新值:</span>
                                                                                <span class="change-value bg-light px-2 py-1 rounded" x-html="formatChangeValue(change.newValue)"></span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="change-actions">
                                                                        <button class="btn btn-sm"
                                                                                :class="change.confirmed ? 'btn-success' : 'btn-outline-secondary'"
                                                                                @click="toggleChangeConfirmation(change)"
                                                                                :title="change.confirmed ? '取消确认' : '确认变更'">
                                                                            <i :class="change.confirmed ? 'fas fa-check' : 'far fa-square'"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </div>
                                            </div>
                                        </template>

                                        <!-- 无变更提示 -->
                                        <div x-show="changeList.length === 0" class="text-center py-5">
                                            <i class="fas fa-info-circle text-muted fa-3x mb-3"></i>
                                            <p class="text-muted">暂无变更数据</p>
                                            <p class="small text-muted">请先加载示例数据进行对比</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：控制面板 -->
                <div class="col-lg-2">
                    <!-- 统计信息 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <i class="fas fa-chart-pie me-1"></i>
                            变更统计
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number text-success" x-text="getChangeCount('ADD')"></div>
                                        <div class="stat-label">新增</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number text-warning" x-text="getChangeCount('MODIFY')"></div>
                                        <div class="stat-label">修改</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number text-danger" x-text="getChangeCount('DELETE')"></div>
                                        <div class="stat-label">删除</div>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <span>已确认变更:</span>
                                <span class="fw-bold" x-text="getConfirmedChangeCount() + '/' + changeList.length"></span>
                            </div>
                        </div>
                    </div>

                    <!-- 配置面板 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <i class="fas fa-cog me-1"></i>
                            配置设置
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-primary btn-sm w-100 mb-2 position-relative" @click="showConfigModal()">
                                <i class="fas fa-key me-1"></i>
                                主键配置
                                <span x-show="getConfigErrorCount() > 0"
                                      class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                                      :title="'发现 ' + getConfigErrorCount() + ' 个配置错误'"
                                      x-text="getConfigErrorCount()">
                                </span>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm w-100" @click="recompareData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                重新对比
                            </button>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-play me-1"></i>
                            操作
                        </div>
                        <div class="card-body">
                            <button class="btn btn-success w-100 mb-2" 
                                    @click="autoSubmit()" 
                                    :disabled="getConfirmedChangeCount() === 0">
                                <i class="fas fa-upload me-1"></i>
                                自动录入 (<span x-text="getConfirmedChangeCount()"></span>项)
                            </button>
                            <button class="btn btn-outline-info w-100" @click="exportChangeList()">
                                <i class="fas fa-download me-1"></i>
                                导出变更列表
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 映射预览模态框 -->
        <div class="modal fade" id="mappingPreviewModal" tabindex="-1" x-data="mappingPreview" @apply-mappings="applyUserMappings($event.detail.moduleName, $event.detail.userMappings)">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-project-diagram me-2"></i>
                            数组映射预览 - <span x-text="currentModule"></span>
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" @click="hide()"></button>
                    </div>
                    <div class="modal-body" x-show="isVisible">
                        <!-- 映射统计 -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body py-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="mapping-stats">
                                                <span class="badge bg-primary me-2" x-text="getMappingStats().total + ' 个映射'"></span>
                                                <span class="badge bg-success me-2" x-text="getMappingStats().confirmed + ' 已确认'"></span>
                                                <span class="badge bg-warning text-dark me-2" x-text="getMappingStats().pending + ' 待确认'"></span>
                                            </div>
                                            <div class="mapping-actions">
                                                <button class="btn btn-sm btn-outline-secondary me-2" @click="resetMappings()">
                                                    <i class="fas fa-undo me-1"></i>重置
                                                </button>
                                                <button class="btn btn-sm btn-success" @click="applyMappings()">
                                                    <i class="fas fa-check me-1"></i>应用映射
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 映射内容 -->
                        <div class="row" x-show="mappingResult">
                            <!-- 源数组 -->
                            <div class="col-md-5">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-list me-1"></i>源数组项目
                                </h6>

                                <!-- 未匹配的源项目 -->
                                <template x-for="item in mappingResult?.unmatched?.source || []" :key="'source_' + item.index">
                                    <div class="mapping-item source-item mb-2"
                                         :class="'status-' + getSourceMappingStatus(item.index)"
                                         draggable="true"
                                         @dragstart="startDrag(item, 'source', $event)"
                                         @dragend="endDrag()">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="item-info flex-grow-1">
                                                <div class="item-title" x-text="getItemDisplayText(item, mappingResult?.keyFields || [])"></div>
                                                <div class="item-index small text-muted">索引: <span x-text="item.index"></span></div>
                                            </div>
                                            <div class="item-actions">
                                                <button class="btn btn-sm btn-outline-danger"
                                                        @click="markAsDeleted(item.index)"
                                                        title="标记为删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </template>

                                <!-- 已匹配的源项目 -->
                                <template x-for="mapping in userMappings.filter(m => m.sourceIndex !== null)" :key="mapping.id">
                                    <div class="mapping-item source-item mapped mb-2"
                                         :class="mapping.needsConfirmation ? 'needs-confirmation' : ''">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="item-info flex-grow-1">
                                                <div class="item-title">源项目 <span x-text="mapping.sourceIndex"></span></div>
                                                <div class="mapping-info small">
                                                    <span class="badge me-1"
                                                          :class="mapping.type === 'exact' ? 'bg-success' : mapping.type === 'fuzzy' ? 'bg-warning text-dark' : 'bg-info'"
                                                          x-text="mapping.type === 'exact' ? '精确匹配' : mapping.type === 'fuzzy' ? '模糊匹配' : '手动映射'"></span>
                                                    <span x-show="mapping.confidence"
                                                          :class="getConfidenceClass(mapping.confidence)"
                                                          x-text="'置信度: ' + formatConfidence(mapping.confidence)"></span>
                                                </div>
                                            </div>
                                            <div class="item-actions">
                                                <template x-if="mapping.needsConfirmation">
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-success"
                                                                @click="confirmFuzzyMatch(mapping.id, true)"
                                                                title="确认匹配">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger"
                                                                @click="confirmFuzzyMatch(mapping.id, false)"
                                                                title="拒绝匹配">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </template>
                                                <template x-if="!mapping.needsConfirmation">
                                                    <button class="btn btn-sm btn-outline-secondary"
                                                            @click="removeMapping(mapping.id)"
                                                            title="移除映射">
                                                        <i class="fas fa-unlink"></i>
                                                    </button>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>

                            <!-- 映射连接区域 -->
                            <div class="col-md-2 d-flex align-items-center justify-content-center">
                                <div class="mapping-connector">
                                    <i class="fas fa-arrows-alt-h fa-2x text-muted"></i>
                                    <div class="small text-muted mt-2 text-center">拖拽建立映射</div>
                                </div>
                            </div>

                            <!-- 目标数组 -->
                            <div class="col-md-5">
                                <h6 class="text-success mb-3">
                                    <i class="fas fa-list me-1"></i>目标数组项目
                                </h6>

                                <!-- 未匹配的目标项目 -->
                                <template x-for="item in mappingResult?.unmatched?.target || []" :key="'target_' + item.index">
                                    <div class="mapping-item target-item mb-2"
                                         :class="'status-' + getTargetMappingStatus(item.index)"
                                         @dragover.prevent
                                         @drop="handleDrop(item, 'target', $event)">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="item-info flex-grow-1">
                                                <div class="item-title" x-text="getItemDisplayText(item, mappingResult?.keyFields || [])"></div>
                                                <div class="item-index small text-muted">索引: <span x-text="item.index"></span></div>
                                            </div>
                                            <div class="item-actions">
                                                <button class="btn btn-sm btn-outline-success"
                                                        @click="markAsNew(item.index)"
                                                        title="标记为新增">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </template>

                                <!-- 已匹配的目标项目 -->
                                <template x-for="mapping in userMappings.filter(m => m.targetIndex !== null)" :key="mapping.id + '_target'">
                                    <div class="mapping-item target-item mapped mb-2"
                                         :class="mapping.needsConfirmation ? 'needs-confirmation' : ''">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="item-info flex-grow-1">
                                                <div class="item-title">目标项目 <span x-text="mapping.targetIndex"></span></div>
                                                <div class="mapping-info small">
                                                    <span class="badge me-1"
                                                          :class="mapping.type === 'exact' ? 'bg-success' : mapping.type === 'fuzzy' ? 'bg-warning text-dark' : 'bg-info'"
                                                          x-text="mapping.type === 'exact' ? '精确匹配' : mapping.type === 'fuzzy' ? '模糊匹配' : '手动映射'"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置模态框 -->
        <div class="modal fade" id="configModal" tabindex="-1" aria-labelledby="configModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="configModalLabel">主键配置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p class="text-muted">配置列表数据的主键字段，用于智能匹配数组项的变更。</p>

                        <!-- 错误摘要 -->
                        <div x-show="getConfigErrorCount() > 0" class="alert alert-warning mb-3">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>发现 <span x-text="getConfigErrorCount()"></span> 个配置错误</strong>
                                    </div>
                                    <div class="small mt-2">
                                        <template x-for="error in getConfigErrors()" :key="error.module">
                                            <div class="text-danger d-flex align-items-center justify-content-between">
                                                <div>
                                                    <i class="fas fa-times-circle me-1"></i>
                                                    <strong x-text="error.module"></strong>: <span x-text="error.message"></span>
                                                </div>
                                                <button x-show="error.canAutoFix"
                                                        class="btn btn-sm btn-outline-primary ms-2"
                                                        @click="autoFixConfigError(error.module)"
                                                        title="自动修复此错误">
                                                    <i class="fas fa-magic"></i>
                                                </button>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                <div class="ms-3">
                                    <button class="btn btn-sm btn-primary"
                                            @click="autoFixAllErrors()"
                                            :disabled="getConfigErrors().filter(e => e.canAutoFix).length === 0"
                                            title="自动修复所有可修复的错误">
                                        <i class="fas fa-magic me-1"></i>
                                        一键修复
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 成功提示 -->
                        <div x-show="getConfigErrorCount() === 0 && dataLoaded" class="alert alert-success mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>所有主键配置均有效</span>
                            </div>
                        </div>
                        <template x-for="(config, moduleName) in keyConfig" :key="moduleName">
                            <div class="mb-3">
                                <label class="form-label fw-bold" x-text="moduleName"></label>
                                <div class="input-group">
                                    <input type="text" class="form-control"
                                           :class="getConfigValidation(moduleName).valid ? '' : 'is-invalid'"
                                           :value="config.keyFields.join(', ')"
                                           @input="updateKeyConfig(moduleName, $event.target.value)"
                                           placeholder="输入主键字段，用逗号分隔">
                                    <button class="btn btn-outline-secondary" type="button" @click="resetKeyConfig(moduleName)" title="重置为默认配置">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="btn btn-outline-info" type="button"
                                            @click="analyzeModuleFields(moduleName)"
                                            title="分析字段建议"
                                            x-show="dataLoaded">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    <span>当前配置: <code x-text="config.keyFields.join(', ') || '无'"></code></span>
                                    <span class="ms-3"
                                          :class="getConfigValidation(moduleName).valid ? 'text-success' : 'text-danger'"
                                          x-text="getConfigValidation(moduleName).message"></span>
                                </div>
                                <div class="form-text text-info" x-show="config.description" x-text="config.description"></div>

                                <!-- 字段建议 -->
                                <div x-show="moduleName === currentAnalyzingModule && fieldSuggestions.length > 0" class="mt-2">
                                    <small class="text-muted">字段建议:</small>
                                    <div class="d-flex flex-wrap gap-1 mt-1">
                                        <template x-for="field in fieldSuggestions" :key="field">
                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                    @click="addFieldToConfig(moduleName, field)"
                                                    x-text="field"></button>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" @click="saveConfig()">保存配置</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 库 -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="./lib/<EMAIL>" defer></script>
    
    <!-- 应用脚本 -->
    <script src="js/key-generator.js"></script>
    <script src="js/array-mapping-manager.js"></script>
    <script src="js/mapping-preview.js"></script>
    <script src="js/data-compare.js"></script>
    <script src="js/followup-diff.js"></script>
</body>
</html>
