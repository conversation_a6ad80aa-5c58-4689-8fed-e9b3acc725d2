/* globals Alpine KeyGenerator DataComparer bootstrap */
/**
 * 病例随访数据对比工具主应用
 * 使用Alpine.js实现响应式数据绑定和状态管理
 */

// Alpine.js 数据存储
document.addEventListener('alpine:init', () => {
    // 主应用数据
    Alpine.data('followupDiffApp', () => ({
        // 状态管理
        loading: false,
        error: null,
        dataLoaded: false,

        // 数据
        initialReport: null,
        followupReport: null,
        finalData: null,
        changeList: [],

        // 分层对比结果
        layeredResult: null,
        currentLayer: 1, // 当前查看的层级 (1 或 2)

        // 配置
        keyConfig: {},
        arrayMappingConfig: {}, // 数组对象映射配置

        // 字段分析
        currentAnalyzingModule: null,
        fieldSuggestions: [],

        // 工具实例
        dataComparer: null,
        keyGenerator: null,
        arrayMappingManager: null,

        // 预览和映射状态
        showPreview: false,
        pendingMappings: [],
        currentMappingModule: null,

        // 初始化
        init() {
            this.keyGenerator = new KeyGenerator();
            this.keyConfig = this.keyGenerator.getDefaultConfig();
            this.arrayMappingManager = new ArrayMappingManager();
            this.dataComparer = new DataComparer(this.keyConfig);

            console.log('病例随访数据对比工具已初始化');
        },

        // 加载示例数据
        async loadSampleData() {
            this.loading = true;
            this.error = null;

            try {
                const [initial, followup, final] = await Promise.all([
                    this.fetchData('data/report-000.json'),
                    this.fetchData('data/report-001.json'),
                    this.fetchData('data/report-002.json')
                ]);

                this.initialReport = initial;
                this.followupReport = followup;
                this.finalData = final;
                this.dataLoaded = true;

                // 执行数据对比
                this.performComparison();

                console.log('数据加载完成', {
                    initial: this.initialReport,
                    followup: this.followupReport,
                    final: this.finalData
                });

            } catch (err) {
                this.error = `数据加载失败: ${err.message}`;
                console.error('数据加载错误:', err);
            } finally {
                this.loading = false;
            }
        },

        // 获取数据
        async fetchData(url) {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.json();
        },

        // 执行数据对比
        performComparison() {
            if (!this.initialReport || !this.followupReport || !this.finalData) {
                return;
            }

            // 使用新的分层对比方法
            this.layeredResult = this.dataComparer.compareLayered(
                this.initialReport,
                this.followupReport,
                this.finalData
            );

            // 为了向后兼容，保持changeList
            this.changeList = this.layeredResult.allChanges;

            console.log('分层数据对比完成:', {
                firstLayerChanges: this.layeredResult.firstLayer.changes.length,
                secondLayerChanges: this.layeredResult.secondLayer.changes.length,
                totalChanges: this.changeList.length
            });
        },

        // 重新对比数据
        recompareData() {
            this.performComparison();
        },

        // 切换查看层级
        switchLayer(layer) {
            this.currentLayer = layer;
        },

        // 获取当前层级的变更列表
        getCurrentLayerChanges() {
            if (!this.layeredResult) return [];

            if (this.currentLayer === 1) {
                return this.layeredResult.firstLayer.changes;
            } else if (this.currentLayer === 2) {
                return this.layeredResult.secondLayer.changes;
            }

            return this.layeredResult.allChanges;
        },

        // 获取当前层级的数据用于展示
        getCurrentLayerData() {
            if (!this.layeredResult) return null;

            if (this.currentLayer === 1) {
                return {
                    source: this.initialReport,
                    target: this.followupReport,
                    merged: this.layeredResult.firstLayer.mergedData
                };
            } else if (this.currentLayer === 2) {
                return {
                    source: this.layeredResult.firstLayer.mergedData,
                    target: this.finalData,
                    merged: null // 第二层不需要合并数据
                };
            }

            return null;
        },

        // 获取层级标题
        getLayerTitle(layer) {
            const titles = {
                1: '第一层对比：初次报告 → 随访报告',
                2: '第二层对比：合并结果 → 已录入数据'
            };
            return titles[layer] || '全部变更';
        },

        // 获取层级描述
        getLayerDescription(layer) {
            const descriptions = {
                1: '对比初次报告和随访报告之间的差异',
                2: '对比第一层合并结果与已录入数据之间的差异'
            };
            return descriptions[layer] || '显示所有层级的变更';
        },

        // 渲染数据阶段
        renderDataStage(data, stage) {
            if (!data) return '<p class="text-muted">暂无数据</p>';

            let html = '';
            Object.keys(data).forEach(moduleName => {
                if (moduleName === 'datetime') return; // 跳过元数据

                const moduleData = data[moduleName];
                html += this.renderModule(moduleName, moduleData, stage);
            });

            return html;
        },

        // 渲染模块
        renderModule(moduleName, moduleData, stage) {
            let html = `
                <div class="data-module">
                    <div class="data-module-header">${moduleName}</div>
                    <div class="data-module-body">
            `;

            if (Array.isArray(moduleData)) {
                html += this.renderArray(moduleData, moduleName, stage);
            } else if (typeof moduleData === 'object' && moduleData !== null) {
                html += this.renderObject(moduleData, stage);
            } else {
                html += `<div class="field-item">
                    <span class="field-label">值</span>
                    <span class="field-value">${this.escapeHtml(String(moduleData))}</span>
                </div>`;
            }

            html += '</div></div>';
            return html;
        },

        // 渲染对象
        renderObject(obj, stage, level = 0) {
            let html = '';
            const maxDisplayFields = level === 0 ? 10 : 5; // 限制显示字段数量
            const keys = Object.keys(obj);
            const displayKeys = keys.slice(0, maxDisplayFields);
            const hiddenCount = keys.length - maxDisplayFields;

            displayKeys.forEach(key => {
                const value = obj[key];
                const changeClass = this.getFieldChangeClass(key, stage);
                const levelClass = level > 0 ? `nested-level-${Math.min(level, 3)}` : '';

                if (Array.isArray(value)) {
                    // 数组类型 - 紧凑显示，换行布局
                    html += `<div class="field-item complex-field ${changeClass} ${levelClass}">
                        <div class="field-label-compact">${this.escapeHtml(key)}</div>
                        <div class="field-value-compact">
                            ${this.renderArrayAsFieldsCompact(value, stage, level + 1)}
                        </div>
                    </div>`;
                } else if (typeof value === 'object' && value !== null) {
                    // 嵌套对象 - 紧凑显示，换行布局
                    html += `<div class="field-item complex-field ${changeClass} ${levelClass}">
                        <div class="field-label-compact">${this.escapeHtml(key)}</div>
                        <div class="field-value-compact">
                            <div class="nested-object-compact">
                                ${this.renderObjectCompact(value, stage, level + 1)}
                            </div>
                        </div>
                    </div>`;
                } else {
                    // 基础类型 - 内联显示
                    html += `<div class="field-item basic-field ${changeClass} ${levelClass}">
                        <span class="field-label-inline">${this.escapeHtml(key)}:</span>
                        <span class="field-value-inline">${this.formatBasicFieldValue(value)}</span>
                    </div>`;
                }
            });

            // 显示隐藏字段提示
            if (hiddenCount > 0) {
                html += `<div class="field-item hidden-fields-indicator">
                    <span class="text-muted small">
                        <i class="fas fa-ellipsis-h me-1"></i>
                        还有 ${hiddenCount} 个字段未显示
                    </span>
                </div>`;
            }

            return html;
        },

        // 紧凑渲染对象（用于嵌套显示）
        renderObjectCompact(obj, stage, level = 0) {
            const keys = Object.keys(obj);
            const maxFields = 3; // 嵌套对象最多显示3个字段
            const displayKeys = keys.slice(0, maxFields);
            const hiddenCount = keys.length - maxFields;

            let html = '<div class="object-fields-compact">';

            displayKeys.forEach((key, index) => {
                const value = obj[key];
                const separator = index < displayKeys.length - 1 ? ', ' : '';

                if (typeof value === 'object' && value !== null) {
                    html += `<span class="field-compact">
                        <strong>${this.escapeHtml(key)}:</strong>
                        <span class="text-info">${Array.isArray(value) ? `[${value.length}项]` : '{对象}'}</span>${separator}
                    </span>`;
                } else {
                    html += `<span class="field-compact">
                        <strong>${this.escapeHtml(key)}:</strong>
                        ${this.formatBasicFieldValue(value)}${separator}
                    </span>`;
                }
            });

            if (hiddenCount > 0) {
                html += `<span class="text-muted">...+${hiddenCount}</span>`;
            }

            html += '</div>';
            return html;
        },

        // 渲染数组
        renderArray(array, moduleName, stage) {
            if (array.length === 0) {
                return '<p class="text-muted small">暂无数据</p>';
            }

            let html = '<div class="array-container">';
            array.forEach((item, index) => {
                html += `<div class="array-item">
                    <div class="array-item-header">项目 ${index + 1}</div>
                    ${this.renderObject(item, stage)}
                </div>`;
            });
            html += '</div>';
            return html;
        },

        // 渲染数组为字段形式（用于嵌套显示）
        renderArrayAsFields(array, stage, level = 0) {
            if (array.length === 0) {
                return '<span class="text-muted small">空列表</span>';
            }

            // 如果是简单数组（字符串、数字等）
            if (array.every(item => typeof item !== 'object')) {
                return `<div class="simple-array-container">
                    ${array.map(item =>
                        `<span class="badge bg-light text-dark">${this.escapeHtml(String(item))}</span>`
                    ).join('')}
                </div>`;
            }

            // 如果是对象数组，紧凑显示每个对象
            let html = '<div class="nested-array-container">';
            array.forEach((item, index) => {
                html += `<div class="nested-array-item">
                    <div class="nested-array-item-header">项目 ${index + 1}</div>
                    <div class="nested-array-item-content">
                        ${this.renderObject(item, stage, level)}
                    </div>
                </div>`;
            });
            html += '</div>';
            return html;
        },

        // 紧凑渲染数组（用于嵌套显示）
        renderArrayAsFieldsCompact(array, stage, level = 0) {
            if (array.length === 0) {
                return '<span class="text-muted small">空列表</span>';
            }

            // 如果是简单数组（字符串、数字等）
            if (array.every(item => typeof item !== 'object')) {
                const maxItems = 5; // 最多显示5个项目
                const displayItems = array.slice(0, maxItems);
                const hiddenCount = array.length - maxItems;

                let html = `<div class="simple-array-compact">
                    ${displayItems.map(item =>
                        `<span class="badge bg-secondary me-1 mb-1">${this.escapeHtml(String(item))}</span>`
                    ).join('')}`;

                if (hiddenCount > 0) {
                    html += `<span class="text-muted small">...+${hiddenCount}</span>`;
                }

                html += '</div>';
                return html;
            }

            // 如果是对象数组，超紧凑显示
            const maxItems = 3; // 最多显示3个对象
            const displayItems = array.slice(0, maxItems);
            const hiddenCount = array.length - maxItems;

            let html = '<div class="object-array-compact">';
            displayItems.forEach((item, index) => {
                html += `<div class="object-item-compact mb-2">
                    <div class="object-item-header-compact">
                        <i class="fas fa-cube me-1"></i>项目 ${index + 1}
                    </div>
                    <div class="object-item-content-compact">
                        ${this.renderObjectCompact(item, stage, level)}
                    </div>
                </div>`;
            });

            if (hiddenCount > 0) {
                html += `<div class="text-muted small text-center">
                    <i class="fas fa-ellipsis-h me-1"></i>
                    还有 ${hiddenCount} 个项目
                </div>`;
            }

            html += '</div>';
            return html;
        },

        // 格式化基础字段值（不包含复杂对象处理）
        formatBasicFieldValue(value) {
            if (value === null || value === undefined || value === '') {
                return '<span class="text-muted">-</span>';
            }
            return this.escapeHtml(String(value));
        },

        // 格式化字段值（已弃用，由renderObject处理复杂类型）
        formatFieldValue(value) {
            // 这个方法现在主要用于向后兼容，复杂对象由renderObject处理
            return this.formatBasicFieldValue(value);
        },



        // 获取字段变更样式类
        getFieldChangeClass(fieldName, stage) {
            // 这里可以根据变更列表来确定样式
            // 简化实现，实际应该检查changeList
            return '';
        },

        // HTML转义
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },

        // 获取变更数量
        getChangeCount(type) {
            return this.changeList.filter(change => change.type === type).length;
        },

        // 获取已确认变更数量
        getConfirmedChangeCount() {
            return this.changeList.filter(change => change.confirmed).length;
        },

        // 按模块分组变更列表
        getChangesByModule() {
            const changes = this.getCurrentLayerChanges();
            const grouped = {};

            changes.forEach(change => {
                const moduleName = this.getModuleNameFromPath(change.path);
                if (!grouped[moduleName]) {
                    grouped[moduleName] = [];
                }
                grouped[moduleName].push(change);
            });

            return grouped;
        },

        // 从路径中提取模块名称
        getModuleNameFromPath(path) {
            const parts = path.split('.');
            return parts[0] || '未知模块';
        },

        // 格式化显示路径（还原转义字符）
        formatDisplayPath(path, moduleName) {
            let displayPath = path.replace(moduleName + '.', '');

            // 还原转义的特殊字符
            displayPath = displayPath
                .replace(/〔/g, '[')   // 恢复左方括号
                .replace(/〕/g, ']')   // 恢复右方括号
                .replace(/％/g, '%')   // 恢复百分号
                .replace(/·/g, '.')   // 恢复点号
                .replace(/｜/g, '|');  // 恢复竖线

            return displayPath;
        },

        // 判断是否应该显示原值
        shouldShowOldValue(change) {
            // 对于删除操作，总是显示原值
            if (change.type === 'DELETE') {
                return true;
            }
            // 对于修改操作，如果有原值就显示
            if (change.type === 'MODIFY') {
                return change.oldValue !== undefined && change.oldValue !== null;
            }
            // 对于新增操作，不显示原值
            return false;
        },

        // 判断是否应该显示新值
        shouldShowNewValue(change) {
            // 对于新增操作，总是显示新值
            if (change.type === 'ADD') {
                return true;
            }
            // 对于修改操作，总是显示新值（包括空值）
            if (change.type === 'MODIFY') {
                return change.newValue !== undefined && change.newValue !== null;
            }
            // 对于删除操作，不显示新值
            return false;
        },

        // 获取模块的变更统计
        getModuleChangeStats(changes) {
            const stats = {
                total: changes.length,
                confirmed: changes.filter(c => c.confirmed).length,
                byType: {
                    ADD: changes.filter(c => c.type === 'ADD').length,
                    MODIFY: changes.filter(c => c.type === 'MODIFY').length,
                    DELETE: changes.filter(c => c.type === 'DELETE').length
                }
            };
            return stats;
        },

        // 切换变更确认状态
        toggleChangeConfirmation(change) {
            change.confirmed = !change.confirmed;
        },

        // 批量确认模块变更
        confirmAllModuleChanges(moduleChanges) {
            moduleChanges.forEach(change => {
                change.confirmed = true;
            });
        },

        // 批量取消模块变更确认
        cancelAllModuleChanges(moduleChanges) {
            moduleChanges.forEach(change => {
                change.confirmed = false;
            });
        },

        // 切换模块所有变更的确认状态
        toggleAllModuleChanges(moduleChanges) {
            const allConfirmed = moduleChanges.every(change => change.confirmed);
            moduleChanges.forEach(change => {
                change.confirmed = !allConfirmed;
            });
        },

        // 获取变更项样式类
        getChangeItemClass(change) {
            return change.confirmed ? 'confirmed' : 'pending';
        },

        // 获取变更类型徽章样式类
        getChangeTypeBadgeClass(type) {
            const classes = {
                'ADD': 'bg-success',
                'MODIFY': 'bg-warning text-dark',
                'DELETE': 'bg-danger'
            };
            return classes[type] || 'bg-secondary';
        },

        // 获取变更类型文本
        getChangeTypeText(type) {
            const texts = {
                'ADD': '新增',
                'MODIFY': '修改',
                'DELETE': '删除'
            };
            return texts[type] || type;
        },

        // 格式化变更值显示
        formatChangeValue(value) {
            // 处理null、undefined和空字符串
            if (value === null || value === undefined) {
                return '<span class="text-muted null-value"><i class="fas fa-ban me-1"></i>空值</span>';
            }
            if (value === '') {
                return '<span class="text-muted empty-value"><i class="fas fa-quote-left me-1"></i>空字符串</span>';
            }

            // 尝试解析JSON格式的值（列表项详情）
            try {
                const parsed = JSON.parse(value);
                if (Array.isArray(parsed)) {
                    return this.formatArrayValueEnhanced(parsed);
                } else if (typeof parsed === 'object' && parsed !== null) {
                    return this.formatObjectValueEnhanced(parsed);
                }
            } catch (e) {
                // 不是JSON，按普通字符串处理
            }

            // 普通值的处理 - 增强显示
            return this.formatBasicValueEnhanced(value);
        },

        // 增强的基础值格式化
        formatBasicValueEnhanced(value) {
            const strValue = String(value);

            // 检测值的类型并添加相应的图标和样式
            if (typeof value === 'number') {
                return `<span class="value-number"><i class="fas fa-hashtag me-1"></i>${this.escapeHtml(strValue)}</span>`;
            }

            if (typeof value === 'boolean') {
                const icon = value ? 'fa-check-circle text-success' : 'fa-times-circle text-danger';
                return `<span class="value-boolean"><i class="fas ${icon} me-1"></i>${this.escapeHtml(strValue)}</span>`;
            }

            // 检测特殊格式
            if (this.isDateString(strValue)) {
                return `<span class="value-date"><i class="fas fa-calendar me-1"></i>${this.escapeHtml(strValue)}</span>`;
            }

            if (this.isEmailString(strValue)) {
                return `<span class="value-email"><i class="fas fa-envelope me-1"></i>${this.escapeHtml(strValue)}</span>`;
            }

            if (this.isUrlString(strValue)) {
                return `<span class="value-url"><i class="fas fa-link me-1"></i>${this.escapeHtml(strValue)}</span>`;
            }

            // 长文本截断
            if (strValue.length > 100) {
                const truncated = strValue.substring(0, 100);
                return `<span class="value-text-long" title="${this.escapeHtml(strValue)}">
                    <i class="fas fa-align-left me-1"></i>
                    ${this.escapeHtml(truncated)}...
                    <small class="text-muted">(${strValue.length}字符)</small>
                </span>`;
            }

            return `<span class="value-text"><i class="fas fa-quote-right me-1"></i>${this.escapeHtml(strValue)}</span>`;
        },

        // 检测日期字符串
        isDateString(str) {
            const dateRegex = /^\d{4}-\d{2}-\d{2}|\d{4}\/\d{2}\/\d{2}|\d{2}\/\d{2}\/\d{4}/;
            return dateRegex.test(str);
        },

        // 检测邮箱字符串
        isEmailString(str) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(str);
        },

        // 检测URL字符串
        isUrlString(str) {
            const urlRegex = /^https?:\/\/.+/;
            return urlRegex.test(str);
        },

        // 增强的数组值格式化
        formatArrayValueEnhanced(array) {
            if (array.length === 0) {
                return '<span class="text-muted array-empty"><i class="fas fa-list me-1"></i>空列表</span>';
            }

            // 如果是简单数组
            if (array.every(item => typeof item !== 'object')) {
                const maxDisplay = 5;
                const displayItems = array.slice(0, maxDisplay);
                const hiddenCount = array.length - maxDisplay;

                let html = `<div class="simple-array-enhanced">
                    <span class="array-indicator">
                        <i class="fas fa-list me-1"></i>
                        <small class="text-muted">[${array.length}项]</small>
                    </span>
                    <div class="array-items mt-1">`;

                html += displayItems.map(item => {
                    const itemType = typeof item;
                    const typeClass = `item-type-${itemType}`;
                    return `<span class="badge bg-light text-dark me-1 mb-1 ${typeClass}">${this.escapeHtml(String(item))}</span>`;
                }).join('');

                if (hiddenCount > 0) {
                    html += `<span class="badge bg-secondary me-1">...+${hiddenCount}</span>`;
                }

                html += '</div></div>';
                return html;
            }

            // 如果是对象数组，紧凑显示
            const maxDisplay = 3;
            const displayItems = array.slice(0, maxDisplay);
            const hiddenCount = array.length - maxDisplay;

            let html = `<div class="object-array-enhanced">
                <div class="array-header">
                    <i class="fas fa-cubes me-1"></i>
                    <strong>对象数组</strong>
                    <small class="text-muted ms-1">[${array.length}项]</small>
                </div>
                <div class="array-content mt-2">`;

            html += displayItems.map((item, index) => {
                return `<div class="object-item-enhanced mb-2">
                    <div class="object-item-header">
                        <i class="fas fa-cube me-1"></i>
                        项目 ${index + 1}
                    </div>
                    <div class="object-item-content">
                        ${this.formatObjectFieldsEnhanced(item)}
                    </div>
                </div>`;
            }).join('');

            if (hiddenCount > 0) {
                html += `<div class="text-center text-muted small">
                    <i class="fas fa-ellipsis-h me-1"></i>
                    还有 ${hiddenCount} 个对象
                </div>`;
            }

            html += '</div></div>';
            return html;
        },

        // 增强的对象值格式化
        formatObjectValueEnhanced(obj) {
            if (Object.keys(obj).length === 0) {
                return '<span class="text-muted object-empty"><i class="fas fa-cube me-1"></i>空对象</span>';
            }

            return `<div class="object-detail-enhanced">
                <div class="object-header">
                    <i class="fas fa-cube me-1"></i>
                    <strong>对象</strong>
                    <small class="text-muted ms-1">[${Object.keys(obj).length}个字段]</small>
                </div>
                <div class="object-content mt-2">
                    ${this.formatObjectFieldsEnhanced(obj)}
                </div>
            </div>`;
        },

        // 增强的对象字段格式化
        formatObjectFieldsEnhanced(obj) {
            const keys = Object.keys(obj);
            const maxFields = 5;
            const displayKeys = keys.slice(0, maxFields);
            const hiddenCount = keys.length - maxFields;

            let html = displayKeys.map(key => {
                const value = obj[key];
                let displayValue;
                let valueClass = '';

                if (Array.isArray(value)) {
                    valueClass = 'field-value-array';
                    if (value.length === 0) {
                        displayValue = '<span class="text-muted"><i class="fas fa-list me-1"></i>空列表</span>';
                    } else if (value.every(item => typeof item !== 'object')) {
                        displayValue = `<span class="text-info"><i class="fas fa-list me-1"></i>${value.length}个简单项</span>`;
                    } else {
                        displayValue = `<span class="text-primary"><i class="fas fa-cubes me-1"></i>${value.length}个对象</span>`;
                    }
                } else if (typeof value === 'object' && value !== null) {
                    valueClass = 'field-value-object';
                    displayValue = `<span class="text-warning"><i class="fas fa-cube me-1"></i>对象(${Object.keys(value).length}字段)</span>`;
                } else {
                    valueClass = 'field-value-basic';
                    displayValue = this.formatBasicValueEnhanced(value);
                }

                return `<div class="field-row-enhanced">
                    <span class="field-name-enhanced">
                        <i class="fas fa-tag me-1"></i>
                        ${this.escapeHtml(key)}:
                    </span>
                    <span class="field-value-enhanced ${valueClass}">${displayValue}</span>
                </div>`;
            }).join('');

            if (hiddenCount > 0) {
                html += `<div class="field-row-enhanced text-muted">
                    <i class="fas fa-ellipsis-h me-1"></i>
                    还有 ${hiddenCount} 个字段未显示
                </div>`;
            }

            return html;
        },

        // 格式化数组值显示（保持向后兼容）
        formatArrayValue(array) {
            return this.formatArrayValueEnhanced(array);
        },

        // 格式化对象值显示（保持向后兼容）
        formatObjectValue(obj) {
            return this.formatObjectValueEnhanced(obj);
        },

        // 格式化对象字段
        formatObjectFields(obj) {
            return Object.keys(obj).map(key => {
                const value = obj[key];
                let displayValue;

                if (Array.isArray(value)) {
                    if (value.length === 0) {
                        displayValue = '<span class="text-muted">空列表</span>';
                    } else if (value.every(item => typeof item !== 'object')) {
                        displayValue = value.map(item =>
                            `<span class="badge bg-secondary me-1">${this.escapeHtml(String(item))}</span>`
                        ).join('');
                    } else {
                        displayValue = `<span class="text-info">${value.length}个对象</span>`;
                    }
                } else if (typeof value === 'object' && value !== null) {
                    displayValue = '<span class="text-warning">对象</span>';
                } else {
                    displayValue = this.escapeHtml(String(value));
                }

                return `<div class="field-row">
                    <span class="field-name">${this.escapeHtml(key)}:</span>
                    <span class="field-value">${displayValue}</span>
                </div>`;
            }).join('');
        },

        // 更新主键配置
        updateKeyConfig(moduleName, value) {
            const fields = value.split(',').map(f => f.trim()).filter(f => f);
            if (!this.keyConfig[moduleName]) {
                this.keyConfig[moduleName] = {};
            }
            this.keyConfig[moduleName].keyFields = fields;

            // 实时验证配置
            this.validateKeyConfig(moduleName);

            // 触发界面更新（Alpine.js会自动重新计算错误数量）
            this.$nextTick(() => {
                // 强制更新按钮上的错误计数
                console.log(`配置更新: ${moduleName}, 当前错误数量: ${this.getConfigErrorCount()}`);
            });
        },

        // 重置主键配置
        resetKeyConfig(moduleName) {
            const defaultConfig = this.keyGenerator.getDefaultConfig();
            if (defaultConfig[moduleName]) {
                this.keyConfig[moduleName] = { ...defaultConfig[moduleName] };
            }
        },

        // 验证主键配置
        validateKeyConfig(moduleName) {
            if (!this.initialReport || !this.keyConfig[moduleName]) {
                return { valid: true, message: '' };
            }

            const moduleData = this.initialReport[moduleName];
            if (!moduleData || !Array.isArray(moduleData) || moduleData.length === 0) {
                return { valid: true, message: '模块无数据' };
            }

            const keyFields = this.keyConfig[moduleName].keyFields;
            if (!keyFields || keyFields.length === 0) {
                return { valid: true, message: '使用索引作为主键' };
            }

            // 检查字段是否存在
            const firstItem = moduleData[0];
            const missingFields = keyFields.filter(field => !(field in firstItem));

            if (missingFields.length > 0) {
                return {
                    valid: false,
                    message: `字段不存在: ${missingFields.join(', ')}`
                };
            }

            // 检查主键唯一性
            const keys = new Set();
            let duplicates = 0;

            moduleData.forEach(item => {
                const key = keyFields.map(field => item[field] || '').join('|');
                if (keys.has(key)) {
                    duplicates++;
                } else {
                    keys.add(key);
                }
            });

            if (duplicates > 0) {
                return {
                    valid: false,
                    message: `发现 ${duplicates} 个重复主键`
                };
            }

            return { valid: true, message: '配置有效' };
        },

        // 获取配置验证状态
        getConfigValidation(moduleName) {
            return this.validateKeyConfig(moduleName);
        },

        // 获取所有配置错误的数量
        getConfigErrorCount() {
            if (!this.dataLoaded || !this.keyConfig) {
                return 0;
            }

            let errorCount = 0;
            Object.keys(this.keyConfig).forEach(moduleName => {
                const validation = this.validateKeyConfig(moduleName);
                if (!validation.valid) {
                    errorCount++;
                }
            });

            return errorCount;
        },

        // 获取配置错误的详细信息
        getConfigErrors() {
            if (!this.dataLoaded || !this.keyConfig) {
                return [];
            }

            const errors = [];
            Object.keys(this.keyConfig).forEach(moduleName => {
                const validation = this.validateKeyConfig(moduleName);
                if (!validation.valid) {
                    errors.push({
                        module: moduleName,
                        message: validation.message,
                        canAutoFix: this.canAutoFixError(moduleName, validation)
                    });
                }
            });

            return errors;
        },

        // 判断错误是否可以自动修复
        canAutoFixError(moduleName, validation) {
            // 如果是字段不存在的错误，可以尝试自动修复
            if (validation.message && validation.message.includes('字段不存在')) {
                return true;
            }
            // 如果是重复主键错误，可以尝试添加更多字段
            if (validation.message && validation.message.includes('重复主键')) {
                return true;
            }
            return false;
        },

        // 自动修复配置错误
        autoFixConfigError(moduleName) {
            if (!this.initialReport || !this.initialReport[moduleName]) {
                return false;
            }

            const moduleData = this.initialReport[moduleName];
            if (!Array.isArray(moduleData) || moduleData.length === 0) {
                return false;
            }

            // 使用智能分析生成新的主键配置
            const suggestion = this.keyGenerator.generateKeyFieldSuggestions(moduleData);
            if (suggestion && suggestion.keyFields && suggestion.keyFields.length > 0) {
                this.keyConfig[moduleName] = {
                    keyFields: suggestion.keyFields,
                    description: suggestion.description
                };
                console.log(`自动修复 ${moduleName} 的配置:`, suggestion.keyFields);
                return true;
            }

            return false;
        },

        // 批量自动修复所有错误
        autoFixAllErrors() {
            const errors = this.getConfigErrors();
            let fixedCount = 0;

            errors.forEach(error => {
                if (error.canAutoFix) {
                    if (this.autoFixConfigError(error.module)) {
                        fixedCount++;
                    }
                }
            });

            if (fixedCount > 0) {
                console.log(`成功自动修复 ${fixedCount} 个配置错误`);
                // 触发界面更新
                this.$nextTick(() => {
                    console.log(`修复后错误数量: ${this.getConfigErrorCount()}`);
                });
            }

            return fixedCount;
        },

        // 分析模块字段
        analyzeModuleFields(moduleName) {
            if (!this.initialReport || !this.initialReport[moduleName]) {
                this.fieldSuggestions = [];
                return;
            }

            const moduleData = this.initialReport[moduleName];
            if (!Array.isArray(moduleData) || moduleData.length === 0) {
                this.fieldSuggestions = [];
                return;
            }

            // 获取所有字段
            const allFields = new Set();
            moduleData.forEach(item => {
                Object.keys(item).forEach(field => allFields.add(field));
            });

            this.fieldSuggestions = Array.from(allFields).sort();
            this.currentAnalyzingModule = moduleName;
        },

        // 添加字段到配置
        addFieldToConfig(moduleName, field) {
            if (!this.keyConfig[moduleName]) {
                this.keyConfig[moduleName] = { keyFields: [] };
            }

            const currentFields = this.keyConfig[moduleName].keyFields || [];
            if (!currentFields.includes(field)) {
                this.keyConfig[moduleName].keyFields = [...currentFields, field];
                this.validateKeyConfig(moduleName);
            }
        },

        // 显示配置模态框
        showConfigModal() {
            const modal = new bootstrap.Modal(document.getElementById('configModal'));
            modal.show();
        },

        // 保存配置
        saveConfig() {
            this.dataComparer.updateKeyConfig(this.keyConfig);
            this.performComparison();

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('configModal'));
            if (modal) {
                modal.hide();
            }

            // 这里可以添加保存到服务器的逻辑
            console.log('配置已保存:', this.keyConfig);
        },

        // 自动录入
        async autoSubmit() {
            const confirmedChanges = this.changeList.filter(change => change.confirmed);
            
            if (confirmedChanges.length === 0) {
                alert('请先确认要录入的变更项');
                return;
            }

            try {
                const changeListData = {
                    id: `changelist_${Date.now()}`,
                    timestamp: new Date().toISOString(),
                    changes: confirmedChanges,
                    metadata: {
                        totalChanges: this.changeList.length,
                        confirmedChanges: confirmedChanges.length,
                        pendingChanges: this.changeList.length - confirmedChanges.length
                    }
                };

                console.log('准备提交变更列表:', changeListData);
                
                // 这里应该调用实际的API
                // const response = await fetch('/api/followup/auto-submit', {
                //     method: 'POST',
                //     headers: { 'Content-Type': 'application/json' },
                //     body: JSON.stringify(changeListData)
                // });

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                alert(`成功录入 ${confirmedChanges.length} 项变更`);
                
            } catch (err) {
                alert(`录入失败: ${err.message}`);
                console.error('自动录入错误:', err);
            }
        },

        // 导出变更列表
        exportChangeList() {
            const data = {
                exportTime: new Date().toISOString(),
                changeList: this.changeList,
                summary: {
                    total: this.changeList.length,
                    confirmed: this.getConfirmedChangeCount(),
                    byType: {
                        add: this.getChangeCount('ADD'),
                        modify: this.getChangeCount('MODIFY'),
                        delete: this.getChangeCount('DELETE')
                    }
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `changelist_${new Date().toISOString().slice(0, 10)}.json`;
            a.click();
            URL.revokeObjectURL(url);
        },

        // 启动预览模式
        startPreviewMode() {
            this.showPreview = true;
            this.dataComparer.enablePreviewMode();
            this.performComparison();

            // 获取待处理的数组对比
            this.pendingMappings = this.dataComparer.getPendingArrayComparisons();

            console.log('预览模式已启动，发现待处理数组对比:', this.pendingMappings.length);
        },

        // 退出预览模式
        exitPreviewMode() {
            this.showPreview = false;
            this.dataComparer.disablePreviewMode();
            this.pendingMappings = [];
            this.currentMappingModule = null;
        },

        // 开始数组映射配置
        startArrayMapping(moduleName) {
            this.currentMappingModule = moduleName;
            const pendingComparison = this.pendingMappings.find(p => p.moduleName === moduleName);

            if (pendingComparison) {
                // 执行智能匹配
                const mappingResult = this.arrayMappingManager.smartMatch(
                    pendingComparison.arr1,
                    pendingComparison.arr2,
                    pendingComparison.keyFields,
                    moduleName
                );

                console.log('数组映射结果:', mappingResult);
                return mappingResult;
            }

            return null;
        },

        // 应用用户映射配置
        applyUserMappings(moduleName, userMappings) {
            // 处理用户确认的映射
            const changes = this.dataComparer.processUserArrayMappings(moduleName, userMappings);

            // 更新变更列表
            this.changeList.push(...changes);

            // 从待处理列表中移除
            this.pendingMappings = this.pendingMappings.filter(p => p.moduleName !== moduleName);

            console.log(`已应用 ${moduleName} 的用户映射，新增变更:`, changes.length);
        },

        // 获取数组映射建议
        getArrayMappingSuggestions(moduleName) {
            const mapping = this.arrayMappingManager.getMapping(moduleName);
            return mapping ? mapping.suggestions : [];
        },

        // 确认模糊匹配
        confirmFuzzyMatch(changeId, confirmed) {
            const change = this.changeList.find(c => c.id === changeId);
            if (change && change.type === 'FUZZY_MATCH') {
                if (confirmed) {
                    // 转换为正常的修改变更
                    change.type = 'MODIFY';
                    change.metadata.needsConfirmation = false;
                } else {
                    // 标记为需要人工处理
                    change.metadata.rejected = true;
                }
            }
        },

        // 获取需要确认的模糊匹配数量
        getFuzzyMatchCount() {
            return this.changeList.filter(c =>
                c.type === 'FUZZY_MATCH' &&
                c.metadata.needsConfirmation &&
                !c.metadata.rejected
            ).length;
        },

        // 获取待处理的数组映射数量
        getPendingMappingCount() {
            return this.pendingMappings.length;
        },

        // 显示数组映射预览
        showArrayMappingPreview(moduleName) {
            const mappingResult = this.startArrayMapping(moduleName);
            if (mappingResult) {
                // 触发映射预览组件显示
                const previewComponent = Alpine.$data(document.querySelector('[x-data="mappingPreview"]'));
                if (previewComponent) {
                    previewComponent.show(moduleName, mappingResult);

                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('mappingPreviewModal'));
                    modal.show();
                }
            }
        },

        // 处理映射预览的应用事件
        handleApplyMappings(event) {
            const { moduleName, userMappings } = event.detail;
            this.applyUserMappings(moduleName, userMappings);
        }
    }));
});
