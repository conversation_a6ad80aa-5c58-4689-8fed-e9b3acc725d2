<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数组检测测试</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="../css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>数组对象检测测试</h2>
        
        <div class="card">
            <div class="card-header">
                <h5>测试结果</h5>
            </div>
            <div class="card-body">
                <div id="test-results"></div>
                <button id="run-tests" class="btn btn-primary">运行测试</button>
            </div>
        </div>
    </div>

    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="js/key-generator.js"></script>
    <script src="js/array-mapping-manager.js"></script>
    <script src="js/data-compare.js"></script>

    <script>
        // 测试数据
        const testData = {
            initialReport: {
                "与事件相关的实验室检查": [
                    {
                        "检查日期": "2025-06-05",
                        "检查项目名称": "血常规-淋巴细胞比率",
                        "检查结果": "22.00"
                    },
                    {
                        "检查日期": "2025-06-05",
                        "检查项目名称": "血常规-单核细胞比率",
                        "检查结果": "2.90"
                    }
                ]
            },
            followupReport: {
                "与事件相关的实验室检查": [
                    {
                        "检查日期": "2025-06-05",
                        "检查项目名称": "血常规-淋巴细胞比率",
                        "检查结果": "23.00"
                    },
                    {
                        "检查日期": "2025-06-05",
                        "检查项目名称": "血常规-单核细胞比率",
                        "检查结果": "3.10"
                    },
                    {
                        "检查日期": "2025-06-06",
                        "检查项目名称": "血常规-白细胞计数",
                        "检查结果": "6.5"
                    }
                ]
            }
        };

        // 测试配置
        const testConfig = {
            "与事件相关的实验室检查": {
                keyFields: ["检查日期", "检查项目名称"],
                description: "基于检查日期和检查项目名称生成主键"
            }
        };

        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="spinner-border" role="status"><span class="visually-hidden">测试中...</span></div>';

            setTimeout(() => {
                try {
                    console.log('开始测试数组检测...');
                    
                    // 创建数据对比器
                    const dataComparer = new DataComparer(testConfig);
                    
                    // 测试1: 模块名称提取
                    console.log('测试模块名称提取...');
                    const testPaths = [
                        "与事件相关的实验室检查",
                        "与事件相关的实验室检查[0]",
                        "与事件相关的实验室检查[0].检查日期",
                        "与事件相关的实验室检查[血常规-淋巴细胞比率|2025-06-05].检查结果"
                    ];
                    
                    const moduleNameResults = testPaths.map(path => ({
                        path: path,
                        moduleName: dataComparer.getModuleName(path)
                    }));
                    
                    // 测试2: 启用预览模式
                    console.log('启用预览模式...');
                    dataComparer.enablePreviewMode();
                    
                    // 测试3: 执行分层对比
                    console.log('执行分层对比...');
                    const layeredResult = dataComparer.compareLayered(
                        testData.initialReport,
                        testData.followupReport,
                        testData.followupReport // 使用相同数据作为最终数据
                    );
                    
                    // 测试4: 获取待处理的数组对比
                    const pendingComparisons = dataComparer.getPendingArrayComparisons();
                    
                    // 生成测试报告
                    const report = generateTestReport(moduleNameResults, layeredResult, pendingComparisons);
                    resultsDiv.innerHTML = report;

                } catch (error) {
                    resultsDiv.innerHTML = `<div class="alert alert-danger">测试失败: ${error.message}<br><pre>${error.stack}</pre></div>`;
                    console.error('测试错误:', error);
                }
            }, 1000);
        }

        function generateTestReport(moduleNameResults, layeredResult, pendingComparisons) {
            return `
                <div class="test-report">
                    <h6>模块名称提取测试</h6>
                    <div class="table-responsive mb-3">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>路径</th>
                                    <th>提取的模块名</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${moduleNameResults.map(result => `
                                    <tr>
                                        <td><code>${result.path}</code></td>
                                        <td><strong>${result.moduleName}</strong></td>
                                        <td>
                                            ${result.moduleName === "与事件相关的实验室检查" ? 
                                                '<span class="badge bg-success">✓</span>' : 
                                                '<span class="badge bg-danger">✗</span>'}
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <h6>分层对比测试结果</h6>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="text-primary">${layeredResult.firstLayer.changes.length}</h5>
                                    <small>第一层变更</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="text-success">${layeredResult.secondLayer.changes.length}</h5>
                                    <small>第二层变更</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="text-info">${pendingComparisons.length}</h5>
                                    <small>待处理数组对比</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h6>待处理数组对比详情</h6>
                    <div class="table-responsive mb-3">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>模块名称</th>
                                    <th>路径</th>
                                    <th>源数组长度</th>
                                    <th>目标数组长度</th>
                                    <th>主键字段</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${pendingComparisons.map(comp => `
                                    <tr>
                                        <td><strong>${comp.moduleName}</strong></td>
                                        <td><code>${comp.basePath}</code></td>
                                        <td>${comp.arr1.length}</td>
                                        <td>${comp.arr2.length}</td>
                                        <td>${comp.keyFields.join(', ')}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="alert ${pendingComparisons.length > 0 ? 'alert-success' : 'alert-warning'}">
                        <h6><i class="fas fa-${pendingComparisons.length > 0 ? 'check' : 'exclamation-triangle'}-circle me-2"></i>
                            ${pendingComparisons.length > 0 ? '测试通过' : '测试警告'}
                        </h6>
                        <ul class="mb-0">
                            <li>模块名称提取: ${moduleNameResults.every(r => r.moduleName === "与事件相关的实验室检查") ? '✓ 正常' : '✗ 异常'}</li>
                            <li>数组对象检测: ${pendingComparisons.length > 0 ? '✓ 正常' : '✗ 未检测到数组对象'}</li>
                            <li>预览模式: ${pendingComparisons.length > 0 ? '✓ 正常工作' : '✗ 未生成待处理项'}</li>
                        </ul>
                    </div>

                    <details class="mt-3">
                        <summary class="btn btn-outline-secondary btn-sm">查看详细结果</summary>
                        <pre class="mt-2 p-3 bg-light border rounded"><code>${JSON.stringify({layeredResult, pendingComparisons}, null, 2)}</code></pre>
                    </details>
                </div>
            `;
        }

        // 绑定事件
        document.getElementById('run-tests').addEventListener('click', runTests);

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，准备运行测试...');
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
