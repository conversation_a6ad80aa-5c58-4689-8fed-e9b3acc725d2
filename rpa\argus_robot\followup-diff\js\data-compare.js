/**
 * 数据对比算法模块
 * 实现分层数据对比：第一层（初次报告 vs 随访报告）→ 第二层（第一层差异结果 vs 已录入数据）
 */

class DataComparer {
    constructor(keyConfig = {}) {
        this.keyConfig = keyConfig;
        this.changeId = 0;
        this.firstLayerResult = null; // 存储第一层对比结果
        this.arrayMappingConfig = {}; // 数组对象映射配置
        this.arrayMappingManager = new ArrayMappingManager(); // 数组映射管理器
        this.previewMode = false; // 预览模式标志
        this.pendingArrayComparisons = []; // 待处理的数组对比
    }

    /**
     * 执行分层数据对比
     * @param {Object} initialReport - 初次报告
     * @param {Object} followupReport - 随访报告
     * @param {Object} finalData - 已录入数据
     * @returns {Object} 分层对比结果
     */
    compareLayered(initialReport, followupReport, finalData) {
        // 第一层对比：初次报告 vs 随访报告
        const firstLayerChanges = this.compareFirstLayer(initialReport, followupReport);

        // 生成第一层对比后的合并数据
        const mergedData = this.applyChangesToData(initialReport, firstLayerChanges);
        this.firstLayerResult = mergedData;

        // 第二层对比：第一层结果 vs 已录入数据
        const secondLayerChanges = this.compareSecondLayer(mergedData, finalData);

        return {
            firstLayer: {
                changes: firstLayerChanges,
                mergedData: mergedData
            },
            secondLayer: {
                changes: secondLayerChanges
            },
            allChanges: [...firstLayerChanges, ...secondLayerChanges]
        };
    }

    /**
     * 第一层对比：初次报告 vs 随访报告
     * @param {Object} initialReport - 初次报告
     * @param {Object} followupReport - 随访报告
     * @returns {Array} 第一层变更列表
     */
    compareFirstLayer(initialReport, followupReport) {
        return this.compareObjects(
            initialReport,
            followupReport,
            '',
            'layer1_initial_to_followup'
        );
    }

    /**
     * 第二层对比：第一层结果 vs 已录入数据
     * @param {Object} mergedData - 第一层合并后的数据
     * @param {Object} finalData - 已录入数据
     * @returns {Array} 第二层变更列表
     */
    compareSecondLayer(mergedData, finalData) {
        return this.compareObjects(
            mergedData,
            finalData,
            '',
            'layer2_merged_to_final'
        );
    }

    /**
     * 将变更应用到数据上，生成合并后的数据
     * @param {Object} baseData - 基础数据
     * @param {Array} changes - 变更列表
     * @returns {Object} 合并后的数据
     */
    applyChangesToData(baseData, changes) {
        const result = JSON.parse(JSON.stringify(baseData)); // 深拷贝

        changes.forEach(change => {
            if (change.type === 'ADD' || change.type === 'MODIFY') {
                this.setNestedValue(result, change.path, this.parseValue(change.newValue));
            } else if (change.type === 'DELETE') {
                this.deleteNestedValue(result, change.path);
            }
        });

        return result;
    }

    /**
     * 兼容旧版本的三阶段对比方法
     * @param {Object} initialReport - 初次报告
     * @param {Object} followupReport - 随访报告
     * @param {Object} finalData - 已录入数据
     * @returns {Array} 变更列表（为了向后兼容）
     */
    compareThreeStages(initialReport, followupReport, finalData) {
        const layeredResult = this.compareLayered(initialReport, followupReport, finalData);
        return layeredResult.allChanges;
    }

    /**
     * 比较两个对象
     * @param {Object} obj1 - 源对象
     * @param {Object} obj2 - 目标对象
     * @param {string} basePath - 基础路径
     * @param {string} stage - 对比阶段
     * @returns {Array} 变更列表
     */
    compareObjects(obj1, obj2, basePath = '', stage = '') {
        const changes = [];
        const allKeys = new Set([...Object.keys(obj1 || {}), ...Object.keys(obj2 || {})]);

        for (const key of allKeys) {
            const currentPath = basePath ? `${basePath}.${key}` : key;
            const val1 = obj1?.[key];
            const val2 = obj2?.[key];

            if (val1 === undefined && val2 !== undefined) {
                // 新增字段
                changes.push(this.createChange('ADD', currentPath, null, val2, stage));
            } else if (val1 !== undefined && val2 === undefined) {
                // 删除字段
                changes.push(this.createChange('DELETE', currentPath, val1, null, stage));
            } else if (val1 !== undefined && val2 !== undefined) {
                if (Array.isArray(val1) && Array.isArray(val2)) {
                    // 数组对比
                    const arrayChanges = this.compareArrays(val1, val2, currentPath, stage);
                    changes.push(...arrayChanges);
                } else if (this.isObject(val1) && this.isObject(val2)) {
                    // 对象递归对比
                    const objectChanges = this.compareObjects(val1, val2, currentPath, stage);
                    changes.push(...objectChanges);
                } else if (val1 !== val2) {
                    // 基础值对比
                    changes.push(this.createChange('MODIFY', currentPath, val1, val2, stage));
                }
            }
        }

        return changes;
    }

    /**
     * 比较数组
     * @param {Array} arr1 - 源数组
     * @param {Array} arr2 - 目标数组
     * @param {string} basePath - 基础路径
     * @param {string} stage - 对比阶段
     * @returns {Array} 变更列表
     */
    compareArrays(arr1, arr2, basePath, stage) {
        const changes = [];
        const moduleName = this.getModuleName(basePath);
        const keyFields = this.keyConfig[moduleName]?.keyFields || [];

        // 检查是否为对象数组，需要智能映射
        // 检查两个数组中的任一个是否包含对象
        const hasObjectsInArr1 = arr1.length > 0 && arr1.some(item => typeof item === 'object' && item !== null && !Array.isArray(item));
        const hasObjectsInArr2 = arr2.length > 0 && arr2.some(item => typeof item === 'object' && item !== null && !Array.isArray(item));
        const isObjectArray = hasObjectsInArr1 || hasObjectsInArr2;

        console.log(`数组对比 - 模块: ${moduleName}, 路径: ${basePath}`);
        console.log(`arr1长度: ${arr1.length}, arr2长度: ${arr2.length}`);
        console.log(`是否对象数组: ${isObjectArray}, 主键字段: ${keyFields.join(', ')}`);

        if (isObjectArray && keyFields.length > 0) {
            console.log(`使用智能映射对比 - ${moduleName}`);
            // 使用智能映射进行对比
            return this.compareArraysWithMapping(arr1, arr2, basePath, stage, keyFields, moduleName);
        } else if (keyFields.length === 0) {
            console.log(`按索引对比 - ${moduleName}`);
            // 没有配置主键，按索引对比
            return this.compareArraysByIndex(arr1, arr2, basePath, stage);
        } else {
            console.log(`按主键对比 - ${moduleName}`);
            // 基于主键对比（非对象数组）
            return this.compareArraysByKey(arr1, arr2, basePath, stage, keyFields);
        }
    }

    /**
     * 使用智能映射比较数组
     * @param {Array} arr1 - 源数组
     * @param {Array} arr2 - 目标数组
     * @param {string} basePath - 基础路径
     * @param {string} stage - 对比阶段
     * @param {Array} keyFields - 主键字段
     * @param {string} moduleName - 模块名称
     * @returns {Array} 变更列表
     */
    compareArraysWithMapping(arr1, arr2, basePath, stage, keyFields, moduleName) {
        const changes = [];

        console.log(`智能映射对比 - 模块: ${moduleName}, 预览模式: ${this.previewMode}`);

        // 如果在预览模式，记录待处理的数组对比
        if (this.previewMode) {
            console.log(`添加到待处理列表 - ${moduleName}`);
            this.pendingArrayComparisons.push({
                arr1, arr2, basePath, stage, keyFields, moduleName
            });
            console.log(`当前待处理数组对比数量: ${this.pendingArrayComparisons.length}`);

            // 在预览模式下生成占位符变更记录，这样模块会在变更详情中显示
            changes.push(this.createChange(
                'PENDING_MAPPING',
                basePath,
                `数组包含 ${arr1.length} 个项目`,
                `数组包含 ${arr2.length} 个项目`,
                stage,
                {
                    moduleName: moduleName,
                    sourceCount: arr1.length,
                    targetCount: arr2.length,
                    keyFields: keyFields,
                    needsMapping: true,
                    isPendingMapping: true
                }
            ));

            return changes; // 返回占位符变更记录
        }

        // 执行智能匹配
        const mappingResult = this.arrayMappingManager.smartMatch(arr1, arr2, keyFields, moduleName);

        // 处理精确匹配
        mappingResult.exactMatches.forEach(match => {
            const safeKey = this.escapePathKey(match.matchKey);
            const itemPath = `${basePath}[${safeKey}]`;
            const itemChanges = this.compareObjects(match.sourceData, match.targetData, itemPath, stage);
            changes.push(...itemChanges);
        });

        // 处理模糊匹配（需要用户确认）
        mappingResult.fuzzyMatches.forEach(match => {
            const itemPath = `${basePath}[fuzzy_${match.sourceIndex}_${match.targetIndex}]`;
            changes.push(this.createChange('FUZZY_MATCH', itemPath, match.sourceData, match.targetData, stage, {
                confidence: match.confidence,
                sourceIndex: match.sourceIndex,
                targetIndex: match.targetIndex,
                needsConfirmation: true
            }));
        });

        // 处理未匹配的源项（删除）
        mappingResult.unmatched.source.forEach(item => {
            const itemPath = `${basePath}[${item.index}]`;
            changes.push(this.createChange('DELETE', itemPath, item.data, null, stage));
        });

        // 处理未匹配的目标项（新增）
        mappingResult.unmatched.target.forEach(item => {
            const itemPath = `${basePath}[new_${item.index}]`;
            changes.push(this.createChange('ADD', itemPath, null, item.data, stage));
        });

        return changes;
    }

    /**
     * 基于主键比较数组（传统方法）
     * @param {Array} arr1 - 源数组
     * @param {Array} arr2 - 目标数组
     * @param {string} basePath - 基础路径
     * @param {string} stage - 对比阶段
     * @param {Array} keyFields - 主键字段
     * @returns {Array} 变更列表
     */
    compareArraysByKey(arr1, arr2, basePath, stage, keyFields) {
        const changes = [];
        const map1 = this.createKeyMap(arr1, keyFields);
        const map2 = this.createKeyMap(arr2, keyFields);
        const allKeys = new Set([...map1.keys(), ...map2.keys()]);

        for (const key of allKeys) {
            const item1 = map1.get(key);
            const item2 = map2.get(key);
            const safeKey = this.escapePathKey(key);
            const itemPath = `${basePath}[${safeKey}]`;

            if (!item1 && item2) {
                changes.push(this.createChange('ADD', itemPath, null, item2.data, stage));
            } else if (item1 && !item2) {
                changes.push(this.createChange('DELETE', itemPath, item1.data, null, stage));
            } else if (item1 && item2) {
                const itemChanges = this.compareObjects(item1.data, item2.data, itemPath, stage);
                changes.push(...itemChanges);
            }
        }

        return changes;
    }

    /**
     * 按索引比较数组
     * @param {Array} arr1 - 源数组
     * @param {Array} arr2 - 目标数组
     * @param {string} basePath - 基础路径
     * @param {string} stage - 对比阶段
     * @returns {Array} 变更列表
     */
    compareArraysByIndex(arr1, arr2, basePath, stage) {
        const changes = [];
        const maxLength = Math.max(arr1.length, arr2.length);

        for (let i = 0; i < maxLength; i++) {
            const item1 = arr1[i];
            const item2 = arr2[i];
            const itemPath = `${basePath}[${i}]`;

            if (item1 === undefined && item2 !== undefined) {
                changes.push(this.createChange('ADD', itemPath, null, item2, stage));
            } else if (item1 !== undefined && item2 === undefined) {
                changes.push(this.createChange('DELETE', itemPath, item1, null, stage));
            } else if (item1 !== undefined && item2 !== undefined) {
                if (this.isObject(item1) && this.isObject(item2)) {
                    const itemChanges = this.compareObjects(item1, item2, itemPath, stage);
                    changes.push(...itemChanges);
                } else if (item1 !== item2) {
                    changes.push(this.createChange('MODIFY', itemPath, item1, item2, stage));
                }
            }
        }

        return changes;
    }

    /**
     * 创建主键映射
     * @param {Array} array - 数组
     * @param {Array} keyFields - 主键字段
     * @returns {Map} 主键映射
     */
    createKeyMap(array, keyFields) {
        const map = new Map();
        
        array.forEach((item, index) => {
            const key = this.generateKey(item, keyFields) || `__index_${index}`;
            map.set(key, { data: item, index });
        });

        return map;
    }

    /**
     * 生成主键
     * @param {Object} item - 数据项
     * @param {Array} keyFields - 主键字段
     * @returns {string} 主键
     */
    generateKey(item, keyFields) {
        if (!keyFields || keyFields.length === 0) {
            return null;
        }

        const keyValues = keyFields.map(field => {
            const value = this.getNestedValue(item, field);
            return value !== undefined ? String(value) : '';
        });

        return keyValues.join('|');
    }

    /**
     * 获取嵌套对象的值
     * @param {Object} obj - 对象
     * @param {string} path - 路径
     * @returns {*} 值
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }

    /**
     * 获取模块名称
     * @param {string} path - 路径
     * @returns {string} 模块名称
     */
    getModuleName(path) {
        const parts = path.split('.');
        let moduleName = parts[0] || '';

        // 移除数组索引部分，如 "与事件相关的实验室检查[0]" -> "与事件相关的实验室检查"
        const arrayIndexMatch = moduleName.match(/^(.+)\[.+\]$/);
        if (arrayIndexMatch) {
            moduleName = arrayIndexMatch[1];
        }

        return moduleName;
    }

    /**
     * 创建变更记录
     * @param {string} type - 变更类型
     * @param {string} path - 路径
     * @param {*} oldValue - 旧值
     * @param {*} newValue - 新值
     * @param {string} stage - 阶段
     * @param {Object} metadata - 额外元数据
     * @returns {Object} 变更记录
     */
    createChange(type, path, oldValue, newValue, stage, metadata = {}) {
        return {
            id: `change_${++this.changeId}`,
            type,
            path,
            oldValue: this.formatValue(oldValue),
            newValue: this.formatValue(newValue),
            stage,
            timestamp: new Date().toISOString(),
            confirmed: false,
            metadata: metadata
        };
    }

    /**
     * 格式化值
     * @param {*} value - 值
     * @returns {string} 格式化后的值
     */
    formatValue(value) {
        if (value === null || value === undefined) {
            return '';
        }
        if (Array.isArray(value)) {
            if (value.length === 0) {
                return '空列表';
            }
            // 如果是简单数组（字符串、数字等）
            if (value.every(item => typeof item !== 'object')) {
                return value.join(', ');
            }
            // 如果是对象数组，返回完整的JSON字符串
            return JSON.stringify(value, null, 2);
        }
        if (typeof value === 'object') {
            return JSON.stringify(value, null, 2);
        }
        return String(value);
    }

    /**
     * 转义路径键中的特殊字符
     * @param {string} key - 原始键
     * @returns {string} 转义后的键
     */
    escapePathKey(key) {
        if (!key || typeof key !== 'string') {
            return key;
        }

        // 将特殊字符替换为安全字符
        return key
            .replace(/\[/g, '〔')  // 替换左方括号
            .replace(/\]/g, '〕')  // 替换右方括号
            .replace(/%/g, '％')   // 替换百分号
            .replace(/\./g, '·')   // 替换点号
            .replace(/\|/g, '｜'); // 替换竖线
    }

    /**
     * 反转义路径键中的特殊字符
     * @param {string} key - 转义后的键
     * @returns {string} 原始键
     */
    unescapePathKey(key) {
        if (!key || typeof key !== 'string') {
            return key;
        }

        return key
            .replace(/〔/g, '[')   // 恢复左方括号
            .replace(/〕/g, ']')   // 恢复右方括号
            .replace(/％/g, '%')   // 恢复百分号
            .replace(/·/g, '.')   // 恢复点号
            .replace(/｜/g, '|');  // 恢复竖线
    }

    /**
     * 判断是否为对象
     * @param {*} value - 值
     * @returns {boolean} 是否为对象
     */
    isObject(value) {
        return value !== null && typeof value === 'object' && !Array.isArray(value);
    }

    /**
     * 设置嵌套对象的值
     * @param {Object} obj - 目标对象
     * @param {string} path - 路径
     * @param {*} value - 值
     */
    setNestedValue(obj, path, value) {
        const keys = this.parsePath(path);
        let current = obj;

        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (key.isArray) {
                if (!Array.isArray(current[key.name])) {
                    current[key.name] = [];
                }
                current = current[key.name];
                if (!current[key.index]) {
                    current[key.index] = {};
                }
                current = current[key.index];
            } else {
                if (!current[key.name]) {
                    current[key.name] = {};
                }
                current = current[key.name];
            }
        }

        const lastKey = keys[keys.length - 1];
        if (lastKey.isArray) {
            if (!Array.isArray(current[lastKey.name])) {
                current[lastKey.name] = [];
            }
            current[lastKey.name][lastKey.index] = value;
        } else {
            current[lastKey.name] = value;
        }
    }

    /**
     * 删除嵌套对象的值
     * @param {Object} obj - 目标对象
     * @param {string} path - 路径
     */
    deleteNestedValue(obj, path) {
        const keys = this.parsePath(path);
        let current = obj;

        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (key.isArray) {
                current = current[key.name]?.[key.index];
            } else {
                current = current[key.name];
            }
            if (!current) return;
        }

        const lastKey = keys[keys.length - 1];
        if (lastKey.isArray) {
            if (Array.isArray(current[lastKey.name])) {
                current[lastKey.name].splice(lastKey.index, 1);
            }
        } else {
            delete current[lastKey.name];
        }
    }

    /**
     * 解析路径为键数组
     * @param {string} path - 路径字符串
     * @returns {Array} 键数组
     */
    parsePath(path) {
        const keys = [];
        const parts = path.split('.');

        parts.forEach(part => {
            const arrayMatch = part.match(/^(.+)\[(.+)\]$/);
            if (arrayMatch) {
                keys.push({
                    name: arrayMatch[1],
                    isArray: true,
                    index: this.parseArrayIndex(arrayMatch[2])
                });
            } else {
                keys.push({
                    name: part,
                    isArray: false
                });
            }
        });

        return keys;
    }

    /**
     * 解析数组索引（可能是数字或主键）
     * @param {string} indexStr - 索引字符串
     * @returns {number|string} 解析后的索引
     */
    parseArrayIndex(indexStr) {
        // 如果是纯数字，返回数字索引
        if (/^\d+$/.test(indexStr)) {
            return parseInt(indexStr, 10);
        }
        // 否则返回主键字符串（需要反转义）
        return this.unescapePathKey(indexStr);
    }

    /**
     * 解析值（从字符串转换为实际类型）
     * @param {string} valueStr - 值字符串
     * @returns {*} 解析后的值
     */
    parseValue(valueStr) {
        if (valueStr === '' || valueStr === null || valueStr === undefined) {
            return null;
        }

        // 尝试解析JSON
        try {
            return JSON.parse(valueStr);
        } catch (e) {
            // 如果不是JSON，返回原字符串
            return valueStr;
        }
    }

    /**
     * 更新主键配置
     * @param {Object} newConfig - 新配置
     */
    updateKeyConfig(newConfig) {
        this.keyConfig = { ...this.keyConfig, ...newConfig };
    }

    /**
     * 更新数组映射配置
     * @param {Object} mappingConfig - 映射配置
     */
    updateArrayMappingConfig(mappingConfig) {
        this.arrayMappingConfig = { ...this.arrayMappingConfig, ...mappingConfig };
    }

    /**
     * 获取第一层对比结果
     * @returns {Object} 第一层对比结果
     */
    getFirstLayerResult() {
        return this.firstLayerResult;
    }

    /**
     * 启用预览模式
     */
    enablePreviewMode() {
        this.previewMode = true;
        this.pendingArrayComparisons = [];
    }

    /**
     * 禁用预览模式
     */
    disablePreviewMode() {
        this.previewMode = false;
    }

    /**
     * 获取待处理的数组对比
     * @returns {Array} 待处理的数组对比列表
     */
    getPendingArrayComparisons() {
        return this.pendingArrayComparisons;
    }

    /**
     * 处理用户确认的数组映射
     * @param {string} moduleName - 模块名称
     * @param {Array} userMappings - 用户映射配置
     * @returns {Array} 处理后的变更列表
     */
    processUserArrayMappings(moduleName, userMappings) {
        const changes = [];
        const pendingComparison = this.pendingArrayComparisons.find(
            comp => comp.moduleName === moduleName
        );

        if (!pendingComparison) {
            return changes;
        }

        const { arr1, arr2, basePath, stage } = pendingComparison;

        // 应用用户映射
        userMappings.forEach(mapping => {
            const { sourceIndex, targetIndex, action } = mapping;

            switch (action) {
                case 'map':
                    // 正常映射对比
                    if (sourceIndex !== null && targetIndex !== null) {
                        const sourceItem = arr1[sourceIndex];
                        const targetItem = arr2[targetIndex];
                        const itemPath = `${basePath}[${sourceIndex}]`;
                        const itemChanges = this.compareObjects(sourceItem, targetItem, itemPath, stage);
                        changes.push(...itemChanges);
                    }
                    break;

                case 'merge':
                    // 合并多个项目
                    // 这里需要更复杂的逻辑来处理合并
                    break;

                case 'delete':
                    // 标记为删除
                    if (sourceIndex !== null) {
                        const sourceItem = arr1[sourceIndex];
                        const itemPath = `${basePath}[${sourceIndex}]`;
                        changes.push(this.createChange('DELETE', itemPath, sourceItem, null, stage));
                    }
                    break;

                case 'new':
                    // 标记为新增
                    if (targetIndex !== null) {
                        const targetItem = arr2[targetIndex];
                        const itemPath = `${basePath}[new_${targetIndex}]`;
                        changes.push(this.createChange('ADD', itemPath, null, targetItem, stage));
                    }
                    break;
            }
        });

        return changes;
    }

    /**
     * 获取数组映射管理器
     * @returns {ArrayMappingManager} 数组映射管理器
     */
    getArrayMappingManager() {
        return this.arrayMappingManager;
    }

    /**
     * 重置数组映射管理器
     */
    resetArrayMappingManager() {
        this.arrayMappingManager = new ArrayMappingManager();
    }
}

// 导出到全局
window.DataComparer = DataComparer;
