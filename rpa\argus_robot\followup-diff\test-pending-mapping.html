<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>待配置映射测试</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="../css/all.min.css" rel="stylesheet">
    <link href="css/followup-diff.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>待配置映射显示测试</h2>
        
        <div class="card">
            <div class="card-header">
                <h5>测试结果</h5>
            </div>
            <div class="card-body">
                <div id="test-results"></div>
                <button id="run-tests" class="btn btn-primary">运行测试</button>
            </div>
        </div>
    </div>

    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="js/key-generator.js"></script>
    <script src="js/array-mapping-manager.js"></script>
    <script src="js/data-compare.js"></script>

    <script>
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="spinner-border" role="status"><span class="visually-hidden">测试中...</span></div>';

            setTimeout(() => {
                try {
                    console.log('开始测试PENDING_MAPPING变更生成...');
                    
                    // 测试数据
                    const testData = {
                        initialReport: {
                            "与事件相关的实验室检查": [
                                {
                                    "检查日期": "2025-06-05",
                                    "检查项目名称": "血常规-淋巴细胞比率",
                                    "检查结果": "22.00"
                                }
                            ]
                        },
                        followupReport: {
                            "与事件相关的实验室检查": [
                                {
                                    "检查日期": "2025-06-05",
                                    "检查项目名称": "血常规-淋巴细胞比率",
                                    "检查结果": "23.00"
                                },
                                {
                                    "检查日期": "2025-06-06",
                                    "检查项目名称": "血常规-白细胞计数",
                                    "检查结果": "6.5"
                                }
                            ]
                        }
                    };

                    // 测试配置
                    const testConfig = {
                        "与事件相关的实验室检查": {
                            keyFields: ["检查日期", "检查项目名称"],
                            description: "基于检查日期和检查项目名称生成主键"
                        }
                    };
                    
                    // 创建数据对比器
                    const dataComparer = new DataComparer(testConfig);
                    
                    // 测试1: 非预览模式（应该正常对比）
                    console.log('测试非预览模式...');
                    const normalResult = dataComparer.compareLayered(
                        testData.initialReport,
                        testData.followupReport,
                        testData.followupReport
                    );
                    
                    // 测试2: 预览模式（应该生成PENDING_MAPPING）
                    console.log('测试预览模式...');
                    dataComparer.enablePreviewMode();
                    const previewResult = dataComparer.compareLayered(
                        testData.initialReport,
                        testData.followupReport,
                        testData.followupReport
                    );
                    
                    // 获取待处理的数组对比
                    const pendingComparisons = dataComparer.getPendingArrayComparisons();
                    
                    // 检查PENDING_MAPPING变更
                    const pendingMappingChanges = previewResult.allChanges.filter(c => c.type === 'PENDING_MAPPING');
                    
                    // 生成测试报告
                    const report = generateTestReport(normalResult, previewResult, pendingComparisons, pendingMappingChanges);
                    resultsDiv.innerHTML = report;

                } catch (error) {
                    resultsDiv.innerHTML = `<div class="alert alert-danger">测试失败: ${error.message}<br><pre>${error.stack}</pre></div>`;
                    console.error('测试错误:', error);
                }
            }, 1000);
        }

        function generateTestReport(normalResult, previewResult, pendingComparisons, pendingMappingChanges) {
            return `
                <div class="test-report">
                    <h6>对比模式测试结果</h6>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="text-primary">${normalResult.allChanges.length}</h5>
                                    <small>正常模式变更数</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="text-success">${previewResult.allChanges.length}</h5>
                                    <small>预览模式变更数</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h6>PENDING_MAPPING变更测试</h6>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h5>${pendingMappingChanges.length}</h5>
                                    <small>PENDING_MAPPING变更</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-warning">
                                <div class="card-body text-center">
                                    <h5>${pendingComparisons.length}</h5>
                                    <small>待处理数组对比</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h5>${pendingMappingChanges.length === pendingComparisons.length ? '✓' : '✗'}</h5>
                                    <small>数量匹配</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h6>PENDING_MAPPING变更详情</h6>
                    <div class="table-responsive mb-3">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>变更ID</th>
                                    <th>类型</th>
                                    <th>路径</th>
                                    <th>模块名称</th>
                                    <th>源数量</th>
                                    <th>目标数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${pendingMappingChanges.map(change => `
                                    <tr>
                                        <td><code>${change.id}</code></td>
                                        <td><span class="badge bg-info">${change.type}</span></td>
                                        <td><code>${change.path}</code></td>
                                        <td><strong>${change.metadata?.moduleName || 'N/A'}</strong></td>
                                        <td>${change.metadata?.sourceCount || 'N/A'}</td>
                                        <td>${change.metadata?.targetCount || 'N/A'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="alert ${pendingMappingChanges.length > 0 ? 'alert-success' : 'alert-danger'}">
                        <h6><i class="fas fa-${pendingMappingChanges.length > 0 ? 'check' : 'times'}-circle me-2"></i>
                            ${pendingMappingChanges.length > 0 ? '测试通过' : '测试失败'}
                        </h6>
                        <ul class="mb-0">
                            <li>PENDING_MAPPING变更生成: ${pendingMappingChanges.length > 0 ? '✓ 正常' : '✗ 失败'}</li>
                            <li>待处理数组对比记录: ${pendingComparisons.length > 0 ? '✓ 正常' : '✗ 失败'}</li>
                            <li>数量一致性: ${pendingMappingChanges.length === pendingComparisons.length ? '✓ 正常' : '✗ 不一致'}</li>
                            <li>元数据完整性: ${pendingMappingChanges.every(c => c.metadata?.moduleName) ? '✓ 正常' : '✗ 缺失'}</li>
                        </ul>
                    </div>

                    <details class="mt-3">
                        <summary class="btn btn-outline-secondary btn-sm">查看详细结果</summary>
                        <pre class="mt-2 p-3 bg-light border rounded"><code>${JSON.stringify({
                            normalResult: normalResult.allChanges.length,
                            previewResult: previewResult.allChanges.length,
                            pendingMappingChanges,
                            pendingComparisons
                        }, null, 2)}</code></pre>
                    </details>
                </div>
            `;
        }

        // 绑定事件
        document.getElementById('run-tests').addEventListener('click', runTests);

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，准备运行测试...');
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
