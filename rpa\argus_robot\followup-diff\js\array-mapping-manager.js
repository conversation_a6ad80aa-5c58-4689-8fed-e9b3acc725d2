/**
 * 数组对象映射管理器
 * 处理array<object>类型数据的智能主键匹配和人工干预
 */

class ArrayMappingManager {
    constructor() {
        this.mappings = new Map(); // 存储映射关系
        this.pendingMappings = new Map(); // 待确认的映射
        this.userMappings = new Map(); // 用户手动设置的映射
    }

    /**
     * 智能匹配数组对象
     * @param {Array} sourceArray - 源数组
     * @param {Array} targetArray - 目标数组
     * @param {Array} keyFields - 主键字段
     * @param {string} moduleName - 模块名称
     * @returns {Object} 匹配结果
     */
    smartMatch(sourceArray, targetArray, keyFields, moduleName) {
        const result = {
            exactMatches: [], // 精确匹配
            fuzzyMatches: [], // 模糊匹配
            unmatched: {
                source: [], // 源数组中未匹配的项
                target: []  // 目标数组中未匹配的项
            },
            suggestions: [] // 匹配建议
        };

        // 创建主键映射
        const sourceMap = this.createKeyMap(sourceArray, keyFields);
        const targetMap = this.createKeyMap(targetArray, keyFields);

        // 精确匹配
        for (const [key, sourceItem] of sourceMap) {
            if (targetMap.has(key)) {
                const targetItem = targetMap.get(key);
                result.exactMatches.push({
                    sourceIndex: sourceItem.index,
                    targetIndex: targetItem.index,
                    sourceData: sourceItem.data,
                    targetData: targetItem.data,
                    matchKey: key,
                    confidence: 1.0
                });
                targetMap.delete(key);
            } else {
                result.unmatched.source.push(sourceItem);
            }
        }

        // 剩余的目标项为未匹配
        result.unmatched.target = Array.from(targetMap.values());

        // 模糊匹配
        this.performFuzzyMatching(result, keyFields);

        // 生成匹配建议
        this.generateMatchingSuggestions(result, keyFields);

        // 存储映射结果
        this.mappings.set(moduleName, result);

        return result;
    }

    /**
     * 执行模糊匹配
     * @param {Object} result - 匹配结果对象
     * @param {Array} keyFields - 主键字段
     */
    performFuzzyMatching(result, keyFields) {
        const unmatchedSource = result.unmatched.source;
        const unmatchedTarget = result.unmatched.target;

        unmatchedSource.forEach(sourceItem => {
            unmatchedTarget.forEach(targetItem => {
                const similarity = this.calculateSimilarity(
                    sourceItem.data, 
                    targetItem.data, 
                    keyFields
                );

                if (similarity > 0.7) { // 相似度阈值
                    result.fuzzyMatches.push({
                        sourceIndex: sourceItem.index,
                        targetIndex: targetItem.index,
                        sourceData: sourceItem.data,
                        targetData: targetItem.data,
                        confidence: similarity,
                        matchType: 'fuzzy'
                    });
                }
            });
        });

        // 移除已模糊匹配的项
        result.fuzzyMatches.forEach(match => {
            result.unmatched.source = result.unmatched.source.filter(
                item => item.index !== match.sourceIndex
            );
            result.unmatched.target = result.unmatched.target.filter(
                item => item.index !== match.targetIndex
            );
        });
    }

    /**
     * 计算两个对象的相似度
     * @param {Object} obj1 - 对象1
     * @param {Object} obj2 - 对象2
     * @param {Array} keyFields - 关键字段
     * @returns {number} 相似度 (0-1)
     */
    calculateSimilarity(obj1, obj2, keyFields) {
        let totalScore = 0;
        let fieldCount = 0;

        // 优先比较关键字段
        keyFields.forEach(field => {
            const val1 = this.getNestedValue(obj1, field);
            const val2 = this.getNestedValue(obj2, field);
            
            if (val1 !== undefined && val2 !== undefined) {
                totalScore += this.compareValues(val1, val2);
                fieldCount++;
            }
        });

        // 比较其他字段
        const allFields = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);
        allFields.forEach(field => {
            if (!keyFields.includes(field)) {
                const val1 = obj1[field];
                const val2 = obj2[field];
                
                if (val1 !== undefined && val2 !== undefined) {
                    totalScore += this.compareValues(val1, val2) * 0.5; // 非关键字段权重较低
                    fieldCount += 0.5;
                }
            }
        });

        return fieldCount > 0 ? totalScore / fieldCount : 0;
    }

    /**
     * 比较两个值的相似度
     * @param {*} val1 - 值1
     * @param {*} val2 - 值2
     * @returns {number} 相似度 (0-1)
     */
    compareValues(val1, val2) {
        if (val1 === val2) return 1.0;
        
        if (typeof val1 === 'string' && typeof val2 === 'string') {
            return this.stringSimilarity(val1, val2);
        }
        
        if (typeof val1 === 'number' && typeof val2 === 'number') {
            const diff = Math.abs(val1 - val2);
            const max = Math.max(Math.abs(val1), Math.abs(val2));
            return max > 0 ? 1 - (diff / max) : 1.0;
        }
        
        return 0;
    }

    /**
     * 计算字符串相似度（简化版Levenshtein距离）
     * @param {string} str1 - 字符串1
     * @param {string} str2 - 字符串2
     * @returns {number} 相似度 (0-1)
     */
    stringSimilarity(str1, str2) {
        const len1 = str1.length;
        const len2 = str2.length;
        const maxLen = Math.max(len1, len2);
        
        if (maxLen === 0) return 1.0;
        
        // 简化的编辑距离计算
        let matches = 0;
        const minLen = Math.min(len1, len2);
        
        for (let i = 0; i < minLen; i++) {
            if (str1[i] === str2[i]) {
                matches++;
            }
        }
        
        return matches / maxLen;
    }

    /**
     * 生成匹配建议
     * @param {Object} result - 匹配结果
     * @param {Array} keyFields - 主键字段
     */
    generateMatchingSuggestions(result, keyFields) {
        // 为未匹配的项生成建议
        result.unmatched.source.forEach(sourceItem => {
            result.unmatched.target.forEach(targetItem => {
                const similarity = this.calculateSimilarity(
                    sourceItem.data,
                    targetItem.data,
                    keyFields
                );

                if (similarity > 0.3) { // 较低的阈值用于建议
                    result.suggestions.push({
                        type: 'potential_match',
                        sourceIndex: sourceItem.index,
                        targetIndex: targetItem.index,
                        confidence: similarity,
                        reason: this.generateMatchReason(sourceItem.data, targetItem.data, keyFields)
                    });
                }
            });
        });

        // 为新增项生成建议
        result.unmatched.target.forEach(targetItem => {
            result.suggestions.push({
                type: 'new_item',
                targetIndex: targetItem.index,
                targetData: targetItem.data,
                reason: '目标数组中的新增项'
            });
        });

        // 为删除项生成建议
        result.unmatched.source.forEach(sourceItem => {
            result.suggestions.push({
                type: 'deleted_item',
                sourceIndex: sourceItem.index,
                sourceData: sourceItem.data,
                reason: '源数组中已删除的项'
            });
        });
    }

    /**
     * 生成匹配原因说明
     * @param {Object} sourceData - 源数据
     * @param {Object} targetData - 目标数据
     * @param {Array} keyFields - 主键字段
     * @returns {string} 匹配原因
     */
    generateMatchReason(sourceData, targetData, keyFields) {
        const reasons = [];
        
        keyFields.forEach(field => {
            const val1 = this.getNestedValue(sourceData, field);
            const val2 = this.getNestedValue(targetData, field);
            
            if (val1 && val2) {
                const similarity = this.compareValues(val1, val2);
                if (similarity > 0.8) {
                    reasons.push(`${field}字段高度相似`);
                } else if (similarity > 0.5) {
                    reasons.push(`${field}字段部分相似`);
                }
            }
        });
        
        return reasons.length > 0 ? reasons.join(', ') : '基于整体相似度匹配';
    }

    /**
     * 创建主键映射
     * @param {Array} array - 数组
     * @param {Array} keyFields - 主键字段
     * @returns {Map} 主键映射
     */
    createKeyMap(array, keyFields) {
        const map = new Map();
        
        array.forEach((item, index) => {
            const key = this.generateKey(item, keyFields) || `__index_${index}`;
            map.set(key, { data: item, index });
        });

        return map;
    }

    /**
     * 生成主键
     * @param {Object} item - 数据项
     * @param {Array} keyFields - 主键字段
     * @returns {string} 主键
     */
    generateKey(item, keyFields) {
        if (!keyFields || keyFields.length === 0) {
            return null;
        }

        const keyValues = keyFields.map(field => {
            const value = this.getNestedValue(item, field);
            return value !== undefined ? String(value) : '';
        });

        return keyValues.join('|');
    }

    /**
     * 获取嵌套对象的值
     * @param {Object} obj - 对象
     * @param {string} path - 路径
     * @returns {*} 值
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }

    /**
     * 设置用户手动映射
     * @param {string} moduleName - 模块名称
     * @param {number} sourceIndex - 源索引
     * @param {number} targetIndex - 目标索引
     * @param {string} action - 操作类型 (map, merge, delete, new)
     */
    setUserMapping(moduleName, sourceIndex, targetIndex, action) {
        if (!this.userMappings.has(moduleName)) {
            this.userMappings.set(moduleName, []);
        }
        
        const mappings = this.userMappings.get(moduleName);
        mappings.push({
            sourceIndex,
            targetIndex,
            action,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 获取模块的映射结果
     * @param {string} moduleName - 模块名称
     * @returns {Object} 映射结果
     */
    getMapping(moduleName) {
        return this.mappings.get(moduleName);
    }

    /**
     * 获取用户映射
     * @param {string} moduleName - 模块名称
     * @returns {Array} 用户映射列表
     */
    getUserMappings(moduleName) {
        return this.userMappings.get(moduleName) || [];
    }

    /**
     * 清除模块映射
     * @param {string} moduleName - 模块名称
     */
    clearMapping(moduleName) {
        this.mappings.delete(moduleName);
        this.userMappings.delete(moduleName);
        this.pendingMappings.delete(moduleName);
    }
}

// 导出到全局
window.ArrayMappingManager = ArrayMappingManager;
