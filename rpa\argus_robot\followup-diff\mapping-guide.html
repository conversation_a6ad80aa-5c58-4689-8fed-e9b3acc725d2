<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数组映射配置指南</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="../css/all.min.css" rel="stylesheet">
    <style>
        .guide-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            background-color: #f8f9fa;
        }
        .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            background-color: #0d6efd;
            color: white;
            border-radius: 50%;
            font-weight: bold;
            margin-right: 1rem;
        }
        .mapping-demo {
            border: 2px dashed #dee2e6;
            padding: 1rem;
            border-radius: 0.5rem;
            background-color: #fff;
            margin: 1rem 0;
        }
        .demo-item {
            padding: 0.5rem;
            margin: 0.25rem;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            background-color: #f8f9fa;
            cursor: pointer;
        }
        .demo-source {
            border-left: 4px solid #0d6efd;
        }
        .demo-target {
            border-left: 4px solid #198754;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <h1 class="mb-4">
                    <i class="fas fa-project-diagram me-2"></i>
                    数组映射配置指南
                </h1>

                <!-- 快速开始 -->
                <div class="guide-section">
                    <h3><i class="fas fa-rocket me-2"></i>快速开始</h3>
                    <div class="alert alert-success">
                        <h5>🚀 推荐：使用快速配置</h5>
                        <p class="mb-2">如果您有42个模块需要配置，建议使用 <strong>"快速配置全部"</strong> 按钮：</p>
                        <ol class="mb-0">
                            <li>点击预览模式横幅中的 <span class="badge bg-warning">快速配置全部</span> 按钮</li>
                            <li>系统将自动应用智能匹配策略</li>
                            <li>精确匹配和高置信度模糊匹配将被自动确认</li>
                            <li>未匹配项将被标记为新增或删除</li>
                        </ol>
                    </div>
                </div>

                <!-- 手动配置步骤 -->
                <div class="guide-section">
                    <h3><i class="fas fa-cogs me-2"></i>手动配置步骤</h3>
                    
                    <div class="d-flex align-items-start mb-3">
                        <span class="step-number">1</span>
                        <div>
                            <h5>选择要配置的模块</h5>
                            <p>在 <strong>"变更详情"</strong> 标签页中，找到需要配置映射的模块（显示蓝色映射图标 <i class="fas fa-project-diagram text-primary"></i>）</p>
                        </div>
                    </div>

                    <div class="d-flex align-items-start mb-3">
                        <span class="step-number">2</span>
                        <div>
                            <h5>打开映射配置界面</h5>
                            <p>点击模块标题右侧的 <i class="fas fa-project-diagram"></i> 按钮，打开映射预览模态框</p>
                        </div>
                    </div>

                    <div class="d-flex align-items-start mb-3">
                        <span class="step-number">3</span>
                        <div>
                            <h5>理解映射界面</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <h6 class="text-primary">左侧：源数组项目</h6>
                                    <div class="mapping-demo">
                                        <div class="demo-item demo-source">
                                            <i class="fas fa-cube me-1"></i>
                                            检查项目 1
                                            <small class="text-muted d-block">索引: 0</small>
                                        </div>
                                        <div class="demo-item demo-source">
                                            <i class="fas fa-cube me-1"></i>
                                            检查项目 2
                                            <small class="text-muted d-block">索引: 1</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <h6 class="text-muted">映射连接</h6>
                                    <div class="mapping-demo">
                                        <i class="fas fa-arrows-alt-h fa-2x text-muted"></i>
                                        <div class="small text-muted mt-2">拖拽建立映射</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <h6 class="text-success">右侧：目标数组项目</h6>
                                    <div class="mapping-demo">
                                        <div class="demo-item demo-target">
                                            <i class="fas fa-cube me-1"></i>
                                            检查项目 A
                                            <small class="text-muted d-block">索引: 0</small>
                                        </div>
                                        <div class="demo-item demo-target">
                                            <i class="fas fa-cube me-1"></i>
                                            检查项目 B
                                            <small class="text-muted d-block">索引: 1</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex align-items-start mb-3">
                        <span class="step-number">4</span>
                        <div>
                            <h5>配置映射关系</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-hand-rock me-1"></i>拖拽映射</h6>
                                    <ul>
                                        <li>将左侧项目拖拽到右侧对应项目</li>
                                        <li>建立一对一的映射关系</li>
                                        <li>系统会自动移除冲突的映射</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-question-circle me-1"></i>确认模糊匹配</h6>
                                    <ul>
                                        <li>对于黄色背景的模糊匹配项</li>
                                        <li>点击 <span class="badge bg-success">✓</span> 确认匹配</li>
                                        <li>点击 <span class="badge bg-danger">✗</span> 拒绝匹配</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex align-items-start mb-3">
                        <span class="step-number">5</span>
                        <div>
                            <h5>标记特殊操作</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-trash text-danger me-1"></i>标记删除</h6>
                                    <p>对于源数组中不需要的项目，点击垃圾桶图标标记为删除</p>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-plus text-success me-1"></i>标记新增</h6>
                                    <p>对于目标数组中的新项目，点击加号图标标记为新增</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex align-items-start mb-3">
                        <span class="step-number">6</span>
                        <div>
                            <h5>应用映射配置</h5>
                            <p>配置完成后，点击 <span class="badge bg-success">应用映射</span> 按钮保存配置</p>
                        </div>
                    </div>
                </div>

                <!-- 映射类型说明 -->
                <div class="guide-section">
                    <h3><i class="fas fa-info-circle me-2"></i>映射类型说明</h3>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                                    <h6>精确匹配</h6>
                                    <small>基于主键字段完全匹配</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-question-circle text-warning fa-2x mb-2"></i>
                                    <h6>模糊匹配</h6>
                                    <small>基于相似度算法匹配</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-hand-rock text-info fa-2x mb-2"></i>
                                    <h6>手动映射</h6>
                                    <small>用户拖拽建立的映射</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-secondary">
                                <div class="card-body text-center">
                                    <i class="fas fa-robot text-secondary fa-2x mb-2"></i>
                                    <h6>自动映射</h6>
                                    <small>快速配置生成的映射</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 常见问题 -->
                <div class="guide-section">
                    <h3><i class="fas fa-question-circle me-2"></i>常见问题</h3>
                    
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    为什么有这么多模块需要配置映射？
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    系统检测到数据中包含大量的数组对象（如实验室检查、不良事件等），这些复杂数据结构需要智能映射来确保对比的准确性。
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    快速配置会不会不准确？
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    快速配置使用智能算法，只会自动确认高置信度（>80%）的匹配。对于不确定的项目，您仍然可以在后续手动调整。
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    如何提高映射的准确性？
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    确保在配置模态框中为每个模块设置了正确的主键字段（如"检查日期"+"检查项目名称"），这样系统就能更准确地进行自动匹配。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="index.html" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        返回数据对比工具
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/bootstrap.bundle.min.js"></script>
</body>
</html>
